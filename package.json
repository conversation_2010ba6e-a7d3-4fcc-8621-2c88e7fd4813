{"name": "novel-creation-manager", "version": "1.0.2", "description": "小说创作管理器 - 智能写作助手", "main": "dist/main.js", "homepage": "./", "scripts": {"dev": "concurrently \"npm run dev:vite\" \"wait-on http://localhost:3000 && npm run dev:electron\"", "dev:vite": "vite", "dev:electron": "NODE_ENV=development electron .", "build": "npm run build:main && npm run build:renderer && npm run build:electron", "build:main": "vite build --config electron.vite.config.ts", "build:renderer": "vite build", "build:vite": "vite build", "build:electron": "electron-builder", "preview": "vite preview", "lint": "eslint \"{src,electron}/**/*.{ts,tsx}\" --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint \"{src,electron}/**/*.{ts,tsx}\" --fix", "format": "prettier --write \"{src,electron}/**/*.{ts,tsx}\"", "format:check": "prettier --check \"{src,electron}/**/*.{ts,tsx}\"", "type-check": "tsc --noEmit", "test": "vitest", "test:unit": "vitest --config vitest.config.unit.ts", "test:integration": "vitest --config vitest.config.integration.ts", "test:e2e": "playwright test", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:coverage": "vitest --coverage", "test:ui": "vitest --ui", "test:watch": "vitest --watch", "test:all": "node scripts/test-runner.js all", "test:parallel": "node scripts/test-runner.js parallel", "test:report": "node scripts/test-runner.js report", "test:performance": "lhci autorun", "test:memory": "node --expose-gc scripts/memory-tests.js", "test:a11y": "node scripts/accessibility-tests.js", "analyze:bundle": "vite build --mode analyze", "analyze:deps": "npx depcheck", "audit:fix": "npm audit fix", "clean": "rm -rf dist node_modules/.vite coverage test-results playwright-report", "clean:cache": "rm -rf node_modules/.cache node_modules/.vite", "clean:all": "rm -rf node_modules dist coverage test-results playwright-report", "prepare": "husky install", "lint-staged": "lint-staged", "pre-commit": "lint-staged", "pre-push": "npm run test:unit"}, "keywords": ["novel", "writing", "editor", "ai", "electron", "desktop"], "author": "Your Name", "license": "MIT", "devDependencies": {"@playwright/test": "^1.54.2", "@rollup/plugin-commonjs": "^28.0.6", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-typescript": "^12.1.4", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@typescript-eslint/eslint-plugin": "^8.39.0", "@typescript-eslint/parser": "^8.39.0", "@vitejs/plugin-react": "^4.0.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "concurrently": "^8.0.0", "electron": "^28.0.0", "electron-builder": "^24.0.0", "eslint": "^9.32.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.0.0", "husky": "^8.0.0", "jest": "^29.0.0", "jsdom": "^26.1.0", "lint-staged": "^16.1.4", "playwright": "^1.54.2", "prettier": "^3.0.0", "typescript": "^5.0.0", "vite": "^5.0.0", "vitest": "^3.2.4", "wait-on": "^7.0.0"}, "dependencies": {"@monaco-editor/react": "^4.7.0", "@radix-ui/react-dialog": "^1.0.0", "@radix-ui/react-dropdown-menu": "^2.0.0", "@radix-ui/react-tabs": "^1.0.0", "@radix-ui/react-toast": "^1.1.0", "@types/uuid": "^10.0.0", "electron-updater": "^6.0.0", "marked": "^11.0.0", "monaco-editor": "^0.52.2", "prisma": "^5.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.0.0", "sql.js": "^1.13.0", "styled-components": "^6.0.0", "uuid": "^11.1.0", "zustand": "^4.4.0"}, "build": {"appId": "com.yourcompany.novel-creation-manager", "productName": "小说创作管理器", "directories": {"output": "release"}, "files": ["dist/**/*"], "publish": null, "asar": true, "mac": {"category": "public.app-category.productivity", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "win": {"target": [{"target": "nsis", "arch": ["x64"]}]}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}]}}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"]}}