# 小说创作管理器环境变量配置模板
# ====================================
# 
# 使用说明：
# 1. 复制此文件为 .env
# 2. 根据实际情况填写各项配置
# 3. 确保 .env 文件已添加到 .gitignore
# 4. 不要将包含真实密钥的文件提交到版本控制
#
# ====================================

# AI Service API Keys
# 请在此处配置您的AI服务API密钥
# 删除前面的#号并填入您的实际API密钥

# OpenAI API Key (https://platform.openai.com/api-keys)
OPENAI_API_KEY=your_openai_api_key_here

# 智谱AI API Key (https://open.bigmodel.cn/)
ZHIPU_API_KEY=your_zhipu_api_key_here

# 阿里云通义千问 API Key (https://dashscope.aliyuncs.com/)
QWEN_API_KEY=your_qwen_api_key_here

# 豆包 API Key (https://ark.cn-beijing.volces.com/)
DOUBAO_API_KEY=your_doubao_api_key_here

# Database Configuration
DATABASE_PATH=./data/novel.db
DATABASE_BACKUP_PATH=./backups

# Application Configuration
NODE_ENV=development
PORT=3000
LOG_LEVEL=info

# Testing Configuration
TEST_ENV=test
TEST_DATABASE_PATH=:memory:
TEST_TIMEOUT=30000
COVERAGE_THRESHOLD=80

# CI/CD Configuration (Optional)
CI=false
GITHUB_TOKEN=your_github_token_here
NPM_TOKEN=your_npm_token_here
SLACK_WEBHOOK_URL=your_slack_webhook_url_here

# Performance Monitoring (Optional)
LIGHTHOUSE_CI_SERVER_URL=
SENTRY_DSN=

# Feature Flags (Optional)
ENABLE_AI_FEATURES=true
ENABLE_AUTO_SAVE=true
ENABLE_ANALYTICS=false
ENABLE_DEBUG_MODE=false