import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'
import nodeResolve from '@rollup/plugin-node-resolve'
import commonjs from '@rollup/plugin-commonjs'
import typescript from '@rollup/plugin-typescript'

export default defineConfig({
  plugins: [react()],
  base: './',
  server: {
    port: 3000,
    strictPort: true,
    host: true,
  },
  preview: {
    port: 3000,
    strictPort: true,
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@/components': resolve(__dirname, 'src/components'),
      '@/utils': resolve(__dirname, 'src/utils'),
      '@/stores': resolve(__dirname, 'src/stores'),
      '@/types': resolve(__dirname, 'src/types'),
    },
  },
  build: {
    outDir: 'dist/renderer',
    sourcemap: true,
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'src/main/index.ts'),
        renderer: resolve(__dirname, 'index.html'),
      },
      output: {
        format: 'cjs',
        entryFileNames: chunkInfo => {
          if (chunkInfo.name === 'main') {
            return 'main.js'
          }
          return 'assets/[name]-[hash].js'
        },
      },
      plugins: [
        nodeResolve(),
        commonjs(),
        typescript({
          tsconfig: './tsconfig.json',
          declaration: false,
          declarationMap: false,
          compilerOptions: {
            allowImportingTsExtensions: false,
          },
        }),
      ],
    },
  },
})
