import { contextBridge, ipc<PERSON>enderer } from 'electron'

// 定义API接口
const api = {
  // 数据库操作
  database: {
    query: (sql: string, params: (string | number | null)[]) =>
      ipcRenderer.invoke('database:query', sql, params),
    run: (sql: string, params: (string | number | null)[]) =>
      ipcRenderer.invoke('database:run', sql, params),
  },

  // AI服务
  ai: {
    generate: (request: any) => ipcRenderer.invoke('ai:generate', request),
  },

  // 小说操作
  novel: {
    create: (novel: any) => ipcRenderer.invoke('novel:create', novel),
    get: (id: string) => ipcRenderer.invoke('novel:get', id),
    getAll: () => ipcRenderer.invoke('novel:getAll'),
    update: (id: string, updates: any) =>
      ipcRenderer.invoke('novel:update', id, updates),
    delete: (id: string) => ipcRenderer.invoke('novel:delete', id),
  },

  // 章节操作
  chapter: {
    create: (chapter: any) => ipcRenderer.invoke('chapter:create', chapter),
    getByNovel: (novelId: string) => ipcRenderer.invoke('chapter:getByNovel', novelId),
    update: (id: string, updates: any) =>
      ipcRenderer.invoke('chapter:update', id, updates),
    delete: (id: string) => ipcRenderer.invoke('chapter:delete', id),
  },

  // 角色操作
  character: {
    create: (character: any) => ipcRenderer.invoke('character:create', character),
    getByNovel: (novelId: string) =>
      ipcRenderer.invoke('character:getByNovel', novelId),
    update: (id: string, updates: any) =>
      ipcRenderer.invoke('character:update', id, updates),
    delete: (id: string) => ipcRenderer.invoke('character:delete', id),
  },

  // 素材操作
  material: {
    create: (material: any) => ipcRenderer.invoke('material:create', material),
    getAll: () => ipcRenderer.invoke('material:getAll'),
    getByNovel: (novelId: string) => ipcRenderer.invoke('material:getByNovel', novelId),
    update: (id: string, updates: any) =>
      ipcRenderer.invoke('material:update', id, updates),
    delete: (id: string) => ipcRenderer.invoke('material:delete', id),
  },

  // 大纲操作
  outline: {
    create: (outline: any) => ipcRenderer.invoke('outline:create', outline),
    getByNovel: (novelId: string) => ipcRenderer.invoke('outline:getByNovel', novelId),
    update: (id: string, updates: any) =>
      ipcRenderer.invoke('outline:update', id, updates),
    delete: (id: string) => ipcRenderer.invoke('outline:delete', id),
  },

  // 设置操作
  settings: {
    get: () => ipcRenderer.invoke('settings:get'),
    update: (updates: any) => ipcRenderer.invoke('settings:update', updates),
    getAIConfig: (provider: string) =>
      ipcRenderer.invoke('settings:getAIConfig', provider),
    saveAIConfig: (config: any) => ipcRenderer.invoke('settings:saveAIConfig', config),
  },

  // 文件操作
  file: {
    save: (data: string, path: string) => ipcRenderer.invoke('file:save', data, path),
    read: (path: string) => ipcRenderer.invoke('file:read', path),
  },

  // 导出操作
  export: {
    novel: (novelId: string, options: { format: string }) =>
      ipcRenderer.invoke('export:novel', novelId, options),
  },
}

// 暴露API到渲染进程
contextBridge.exposeInMainWorld('api', api)

// 为TypeScript添加类型声明
export type API = typeof api
