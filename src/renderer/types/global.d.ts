import { 
  Novel, 
  Chapter, 
  Character, 
  Material, 
  Outline, 
  AIRequest, 
  AIResponse,
  AppSettings,
  AIServiceConfig 
} from '../../shared/types'

declare global {
  interface Window {
    api: {
      database: {
        query: (sql: string, params: (string | number | null)[]) => Promise<any[]>
        run: (sql: string, params: (string | number | null)[]) => Promise<{ success: boolean }>
      }
      ai: {
        generate: (request: AIRequest) => Promise<AIResponse>
      }
      novel: {
        create: (novel: Novel) => Promise<string>
        get: (id: string) => Promise<Novel | null>
        getAll: () => Promise<Novel[]>
        update: (id: string, updates: Partial<Novel>) => Promise<boolean>
        delete: (id: string) => Promise<boolean>
      }
      chapter: {
        create: (chapter: Chapter) => Promise<string>
        getByNovel: (novelId: string) => Promise<Chapter[]>
        update: (id: string, updates: Partial<Chapter>) => Promise<boolean>
        delete: (id: string) => Promise<boolean>
      }
      character: {
        create: (character: Character) => Promise<string>
        getByNovel: (novelId: string) => Promise<Character[]>
        update: (id: string, updates: Partial<Character>) => Promise<boolean>
        delete: (id: string) => Promise<boolean>
      }
      material: {
        create: (material: Material) => Promise<string>
        getAll: () => Promise<Material[]>
        getByNovel: (novelId: string) => Promise<Material[]>
        update: (id: string, updates: Partial<Material>) => Promise<boolean>
        delete: (id: string) => Promise<boolean>
      }
      outline: {
        create: (outline: Outline) => Promise<string>
        getByNovel: (novelId: string) => Promise<Outline[]>
        update: (id: string, updates: Partial<Outline>) => Promise<boolean>
        delete: (id: string) => Promise<boolean>
      }
      settings: {
        get: () => Promise<AppSettings>
        update: (updates: Partial<AppSettings>) => Promise<boolean>
        getAIConfig: (provider: string) => Promise<AIServiceConfig | null>
        saveAIConfig: (config: AIServiceConfig) => Promise<boolean>
      }
      file: {
        save: (data: string, path: string) => Promise<boolean>
        read: (path: string) => Promise<string | null>
      }
      export: {
        novel: (novelId: string, format: string) => Promise<string>
      }
    }
  }
}

export {}