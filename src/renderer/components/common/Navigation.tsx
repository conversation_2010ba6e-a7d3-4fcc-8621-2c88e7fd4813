import React from 'react'
import styled from 'styled-components'
import { Link, useLocation } from 'react-router-dom'

const NavContainer = styled.div`
  display: flex;
  background: ${props => props.theme.colors.surface};
  border-bottom: 1px solid ${props => props.theme.colors.border};
  padding: 0 ${props => props.theme.spacing.md};
`

const NavItem = styled(Link)<{ active?: boolean }>`
  display: flex;
  align-items: center;
  padding: ${props => props.theme.spacing.md};
  color: ${props =>
    props.active ? props.theme.colors.primary : props.theme.colors.text};
  text-decoration: none;
  border-bottom: 2px solid
    ${props => (props.active ? props.theme.colors.primary : 'transparent')};
  transition: all 0.2s ease;

  &:hover {
    color: ${props => props.theme.colors.primary};
    background: ${props => props.theme.colors.border}20;
  }
`

const NavIcon = styled.span`
  margin-right: ${props => props.theme.spacing.sm};
  font-size: 1.2rem;
`

const Navigation: React.FC = () => {
  const location = useLocation()

  const navItems = [
    { path: '/', label: '我的小说', icon: '📚' },
    { path: '/editor', label: '编辑器', icon: '✏️' },
    { path: '/outline', label: '大纲', icon: '📋' },
    { path: '/materials', label: '素材库', icon: '📁' },
    { path: '/settings', label: '设置', icon: '⚙️' },
  ]

  return (
    <NavContainer>
      {navItems.map(item => (
        <NavItem
          key={item.path}
          to={item.path}
          active={location.pathname === item.path}
        >
          <NavIcon>{item.icon}</NavIcon>
          {item.label}
        </NavItem>
      ))}
    </NavContainer>
  )
}

export default Navigation
