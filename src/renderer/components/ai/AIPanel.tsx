import React, { useState, useCallback } from 'react'
import styled from 'styled-components'
import { useUIStore } from '../../stores/ui'
import { AIRequest } from '../../../shared/types'

const AIPanelContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  background: ${props => props.theme.colors.background};
  border-left: 1px solid ${props => props.theme.colors.border};
`

const AIPanelHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${props => props.theme.spacing.md};
  background: ${props => props.theme.colors.surface};
  border-bottom: 1px solid ${props => props.theme.colors.border};
`

const AIPanelTitle = styled.h3`
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
`

const AIPanelContent = styled.div`
  flex: 1;
  padding: ${props => props.theme.spacing.md};
  overflow-y: auto;
`

const AIButton = styled.button`
  width: 100%;
  padding: ${props => props.theme.spacing.sm};
  margin-bottom: ${props => props.theme.spacing.sm};
  border: 1px solid ${props => props.theme.colors.border};
  background: ${props => props.theme.colors.surface};
  color: ${props => props.theme.colors.text};
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;

  &:hover {
    background: ${props => props.theme.colors.primary}10;
    border-color: ${props => props.theme.colors.primary};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`

const AIButtonTitle = styled.div`
  font-weight: 500;
  margin-bottom: 4px;
`

const AIButtonDescription = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.textSecondary};
`

const AIResponseArea = styled.div`
  margin-top: ${props => props.theme.spacing.md};
  padding: ${props => props.theme.spacing.sm};
  background: ${props => props.theme.colors.surface};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: 6px;
  min-height: 100px;
`

const AIResponseText = styled.div`
  white-space: pre-wrap;
  color: ${props => props.theme.colors.text};
  line-height: 1.5;
`

const LoadingSpinner = styled.div`
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid ${props => props.theme.colors.border};
  border-top: 2px solid ${props => props.theme.colors.primary};
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`

const ProviderSelect = styled.select`
  width: 100%;
  padding: ${props => props.theme.spacing.sm};
  margin-bottom: ${props => props.theme.spacing.md};
  border: 1px solid ${props => props.theme.colors.border};
  background: ${props => props.theme.colors.surface};
  color: ${props => props.theme.colors.text};
  border-radius: 4px;
`

interface AIPanelProps {
  content: string
  onAIResponse: (response: string, type: string) => void
}

const AIPanel: React.FC<AIPanelProps> = ({ content, onAIResponse }) => {
  const { ai: aiSettings } = useUIStore()
  const [selectedProvider, setSelectedProvider] = useState(aiSettings.defaultProvider)
  const [loading, setLoading] = useState(false)
  const [response, setResponse] = useState<string>('')

  const handleAIAction = useCallback(async (type: string) => {
    if (!content.trim()) return

    setLoading(true)
    setResponse('')

    try {
      // 构建 AI 请求
      let prompt = ''
      switch (type) {
        case 'continue':
          prompt = `请继续下面的小说内容，保持相同的文风和叙事节奏：\n\n${content}\n\n继续写作（约200-300字）：`
          break
        case 'polish':
          prompt = `请润色下面的文本，改善文笔和表达方式，使其更加生动流畅：\n\n${content}\n\n润色后的文本：`
          break
        case 'correct':
          prompt = `请检查并纠正下面文本中的错别字、语法错误和标点符号问题：\n\n${content}\n\n纠正后的文本：`
          break
        case 'plot':
          prompt = `基于下面的故事内容，请提供3个可能的剧情发展方向：\n\n${content}\n\n剧情建议：`
          break
        case 'character':
          prompt = `分析下面文本中的人物特征，并提供人物塑造建议：\n\n${content}\n\n人物分析：`
          break
        case 'describe':
          prompt = `为下面的场景添加更多细节描写，使画面更加生动：\n\n${content}\n\n增强描写：`
          break
        default:
          prompt = content
      }

      const aiRequest: AIRequest = {
        prompt,
        temperature: type === 'continue' || type === 'plot' ? 0.8 : 0.5,
        maxTokens: type === 'plot' ? 500 : 1000,
      }

      // 调用 AI 服务
      const aiResponse = await window.api.ai.generate(aiRequest)
      
      if (aiResponse && aiResponse.content) {
        setResponse(aiResponse.content)
        onAIResponse(aiResponse.content, type)
      } else {
        throw new Error('AI response is empty')
      }
    } catch (error) {
      console.error('AI service error:', error)
      setResponse('AI服务暂时不可用，请检查配置或稍后再试。')
    } finally {
      setLoading(false)
    }
  }, [content, onAIResponse])

  const aiActions = [
    {
      type: 'continue',
      title: '智能续写',
      description: '基于当前内容智能续写故事',
      icon: '✍️',
    },
    {
      type: 'polish',
      title: '文本润色',
      description: '改善文笔和表达方式',
      icon: '✨',
    },
    {
      type: 'correct',
      title: '错字纠正',
      description: '自动检测并纠正错别字',
      icon: '🔍',
    },
    {
      type: 'plot',
      title: '剧情推荐',
      description: '提供剧情发展建议',
      icon: '💡',
    },
    {
      type: 'character',
      title: '人物分析',
      description: '分析人物特征和性格',
      icon: '👤',
    },
    {
      type: 'describe',
      title: '增强描写',
      description: '添加更多细节和场景描写',
      icon: '🎨',
    },
  ]

  return (
    <AIPanelContainer>
      <AIPanelHeader>
        <AIPanelTitle>AI 助手</AIPanelTitle>
      </AIPanelHeader>

      <AIPanelContent>
        <ProviderSelect
          value={selectedProvider}
          onChange={e => setSelectedProvider(e.target.value)}
        >
          <option value="openai">OpenAI</option>
          <option value="zhipu">智谱AI</option>
          <option value="qwen">通义千问</option>
          <option value="doubao">豆包</option>
        </ProviderSelect>

        {aiActions.map(action => (
          <AIButton
            key={action.type}
            onClick={() => handleAIAction(action.type)}
            disabled={loading || !content.trim()}
          >
            <AIButtonTitle>
              {action.icon} {action.title}
            </AIButtonTitle>
            <AIButtonDescription>{action.description}</AIButtonDescription>
          </AIButton>
        ))}

        {loading && (
          <AIResponseArea>
            <LoadingSpinner /> AI正在思考中...
          </AIResponseArea>
        )}

        {response && (
          <AIResponseArea>
            <AIResponseText>{response}</AIResponseText>
          </AIResponseArea>
        )}
      </AIPanelContent>
    </AIPanelContainer>
  )
}

export default AIPanel
