import React, { useState, useEffect, useCallback, useRef } from 'react'
import styled from 'styled-components'
import { MonacoEditor } from './MonacoEditor'
import { useNovelStore } from '../../stores/novel'
import { useUIStore } from '../../stores/ui'
import { Chapter } from '../../../shared/types'

const EditorWrapper = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background: ${props => props.theme.colors.background};
`

const EditorHeader = styled.div`
  padding: ${props => props.theme.spacing.md};
  border-bottom: 1px solid ${props => props.theme.colors.border};
  background: ${props => props.theme.colors.surface};
  display: flex;
  align-items: center;
  justify-content: space-between;
`

const ChapterTitle = styled.input`
  font-size: 1.2rem;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  background: transparent;
  border: none;
  outline: none;
  flex: 1;
  margin-right: ${props => props.theme.spacing.md};
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.sm};

  &:hover {
    background: ${props => props.theme.colors.hover};
  }

  &:focus {
    background: ${props => props.theme.colors.hover};
  }
`

const EditorActions = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.sm};
  align-items: center;
`

const ActionButton = styled.button`
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.sm};
  border: 1px solid ${props => props.theme.colors.border};
  background: ${props => props.theme.colors.surface};
  color: ${props => props.theme.colors.text};
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};

  &:hover {
    background: ${props => props.theme.colors.hover};
    border-color: ${props => props.theme.colors.primary};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`

const SaveStatus = styled.span<{ $saved: boolean }>`
  font-size: 0.85rem;
  color: ${props =>
    props.$saved ? props.theme.colors.success : props.theme.colors.textSecondary};
  margin-right: ${props => props.theme.spacing.sm};
`

const EditorContent = styled.div`
  flex: 1;
  overflow: hidden;
`

interface EditorProps {
  chapter: Chapter | null
  onUpdate?: (chapter: Chapter) => void
}

const Editor: React.FC<EditorProps> = ({ chapter, onUpdate }) => {
  const [content, setContent] = useState('')
  const [title, setTitle] = useState('')
  const [isSaving, setIsSaving] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const [wordCount, setWordCount] = useState(0)
  const [characterCount, setCharacterCount] = useState(0)
  
  const autoSaveTimerRef = useRef<NodeJS.Timeout | null>(null)
  const contentChangedRef = useRef(false)
  
  const { updateChapter } = useNovelStore()
  const { autoSave } = useUIStore()

  // 初始化章节内容
  useEffect(() => {
    if (chapter) {
      setContent(chapter.content || '')
      setTitle(chapter.title || '')
      updateWordAndCharacterCount(chapter.content || '')
    } else {
      setContent('')
      setTitle('')
      setWordCount(0)
      setCharacterCount(0)
    }
  }, [chapter?.id])

  // 更新字数和字符统计
  const updateWordAndCharacterCount = (text: string) => {
    // 计算字符数
    setCharacterCount(text.length)
    
    // 计算字数（中文按字算，英文按单词算）
    const chineseChars = text.match(/[\u4e00-\u9fa5]/g) || []
    const englishWords = text.match(/[a-zA-Z]+/g) || []
    const totalWords = chineseChars.length + englishWords.length
    setWordCount(totalWords)
  }

  // 保存章节
  const saveChapter = useCallback(async () => {
    if (!chapter || !contentChangedRef.current) return

    setIsSaving(true)
    try {
      await updateChapter(chapter.id, {
        title,
        content,
        wordCount,
        updatedAt: new Date(),
      })
      
      setLastSaved(new Date())
      contentChangedRef.current = false
      
      // 通知父组件
      if (onUpdate) {
        onUpdate({
          ...chapter,
          title,
          content,
          wordCount,
          updatedAt: new Date(),
        })
      }
    } catch (error) {
      console.error('Failed to save chapter:', error)
    } finally {
      setIsSaving(false)
    }
  }, [chapter, title, content, wordCount, updateChapter, onUpdate])

  // 处理内容变化
  const handleContentChange = useCallback((newContent: string) => {
    setContent(newContent)
    updateWordAndCharacterCount(newContent)
    contentChangedRef.current = true

    // 清除之前的自动保存定时器
    if (autoSaveTimerRef.current) {
      clearTimeout(autoSaveTimerRef.current)
    }

    // 设置新的自动保存定时器
    if (autoSave.enabled) {
      autoSaveTimerRef.current = setTimeout(() => {
        saveChapter()
      }, autoSave.interval)
    }
  }, [autoSave, saveChapter])

  // 处理标题变化
  const handleTitleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setTitle(e.target.value)
    contentChangedRef.current = true

    // 触发自动保存
    if (autoSaveTimerRef.current) {
      clearTimeout(autoSaveTimerRef.current)
    }

    if (autoSave.enabled) {
      autoSaveTimerRef.current = setTimeout(() => {
        saveChapter()
      }, autoSave.interval)
    }
  }, [autoSave, saveChapter])

  // 手动保存
  const handleManualSave = useCallback(() => {
    if (autoSaveTimerRef.current) {
      clearTimeout(autoSaveTimerRef.current)
    }
    saveChapter()
  }, [saveChapter])

  // 复制内容到剪贴板
  const handleCopyContent = useCallback(() => {
    if (content) {
      navigator.clipboard.writeText(content).then(() => {
        // 可以添加一个提示
        console.log('内容已复制到剪贴板')
      }).catch(err => {
        console.error('复制失败:', err)
      })
    }
  }, [content])

  // 导出章节
  const handleExport = useCallback(async () => {
    if (!chapter) return

    const exportContent = `# ${title}\n\n${content}`
    
    // 创建下载链接
    const blob = new Blob([exportContent], { type: 'text/markdown' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${title || '未命名章节'}.md`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }, [chapter, title, content])

  // 清理定时器
  useEffect(() => {
    return () => {
      if (autoSaveTimerRef.current) {
        clearTimeout(autoSaveTimerRef.current)
      }
    }
  }, [])

  // 格式化保存时间
  const formatSaveTime = (date: Date | null) => {
    if (!date) return '未保存'
    
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    const seconds = Math.floor(diff / 1000)
    
    if (seconds < 60) return '刚刚保存'
    if (seconds < 3600) return `${Math.floor(seconds / 60)} 分钟前保存`
    if (seconds < 86400) return `${Math.floor(seconds / 3600)} 小时前保存`
    return date.toLocaleString()
  }

  if (!chapter) {
    return (
      <EditorWrapper>
        <EditorContent style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <div style={{ textAlign: 'center', color: '#999' }}>
            <h3>请选择一个章节开始编辑</h3>
            <p>从左侧章节列表中选择或创建新章节</p>
          </div>
        </EditorContent>
      </EditorWrapper>
    )
  }

  return (
    <EditorWrapper>
      <EditorHeader>
        <ChapterTitle
          value={title}
          onChange={handleTitleChange}
          placeholder="输入章节标题..."
        />
        <EditorActions>
          <SaveStatus $saved={!contentChangedRef.current}>
            {isSaving ? '保存中...' : formatSaveTime(lastSaved)}
          </SaveStatus>
          <ActionButton onClick={handleManualSave} disabled={isSaving || !contentChangedRef.current}>
            💾 保存
          </ActionButton>
          <ActionButton onClick={handleCopyContent} disabled={!content}>
            📋 复制
          </ActionButton>
          <ActionButton onClick={handleExport} disabled={!content}>
            📥 导出
          </ActionButton>
        </EditorActions>
      </EditorHeader>
      <EditorContent>
        <MonacoEditor
          value={content}
          onChange={handleContentChange}
          onSave={handleManualSave}
          wordCount={wordCount}
          characterCount={characterCount}
          language="markdown"
        />
      </EditorContent>
    </EditorWrapper>
  )
}

export default Editor
