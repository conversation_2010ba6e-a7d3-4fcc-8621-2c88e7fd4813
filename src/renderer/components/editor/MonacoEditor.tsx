import React, { useRef, useCallback, useEffect } from 'react'
import Editor, { OnMount, OnChange } from '@monaco-editor/react'
import styled from 'styled-components'
import { useUIStore } from '../../stores/ui'
import { debounce } from '../../utils/debounce'

const EditorContainer = styled.div`
  height: 100%;
  width: 100%;
  position: relative;
`

const StatusBar = styled.div`
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 24px;
  background: ${props => props.theme.colors.surface};
  border-top: 1px solid ${props => props.theme.colors.border};
  display: flex;
  align-items: center;
  padding: 0 ${props => props.theme.spacing.sm};
  font-size: 12px;
  color: ${props => props.theme.colors.textSecondary};
  z-index: 10;
`

const StatusItem = styled.span`
  margin-right: ${props => props.theme.spacing.md};
`

interface MonacoEditorProps {
  value: string
  onChange: (value: string) => void
  onSave?: () => void
  language?: string
  readOnly?: boolean
  wordCount?: number
  characterCount?: number
}

export const MonacoEditor: React.FC<MonacoEditorProps> = ({
  value,
  onChange,
  onSave,
  language = 'markdown',
  readOnly = false,
  wordCount = 0,
  characterCount = 0,
}) => {
  const editorRef = useRef<any>(null)
  const { editor: editorSettings, theme } = useUIStore()

  // 防抖处理onChange
  const debouncedOnChange = useCallback(
    debounce((newValue: string) => {
      onChange(newValue)
    }, 500),
    [onChange]
  )

  const handleEditorDidMount: OnMount = (editor, monaco) => {
    editorRef.current = editor

    // 注册快捷键
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
      onSave?.()
    })

    // 设置编辑器选项
    editor.updateOptions({
      fontSize: editorSettings.fontSize,
      fontFamily: editorSettings.fontFamily,
      lineHeight: editorSettings.lineHeight * editorSettings.fontSize,
      wordWrap: editorSettings.wordWrap ? 'on' : 'off',
      minimap: { enabled: editorSettings.minimap },
      lineNumbers: editorSettings.showLineNumbers ? 'on' : 'off',
      scrollBeyondLastLine: false,
      renderWhitespace: 'selection',
      automaticLayout: true,
    })

    // 焦点管理
    editor.focus()
  }

  const handleEditorChange: OnChange = (value, _event) => {
    if (value !== undefined) {
      debouncedOnChange(value)
    }
  }

  // 监听设置变化
  useEffect(() => {
    if (editorRef.current) {
      editorRef.current.updateOptions({
        fontSize: editorSettings.fontSize,
        fontFamily: editorSettings.fontFamily,
        lineHeight: editorSettings.lineHeight * editorSettings.fontSize,
        wordWrap: editorSettings.wordWrap ? 'on' : 'off',
        minimap: { enabled: editorSettings.minimap },
        lineNumbers: editorSettings.showLineNumbers ? 'on' : 'off',
      })
    }
  }, [editorSettings])

  return (
    <EditorContainer>
      <Editor
        height="calc(100% - 24px)"
        defaultLanguage={language}
        value={value}
        onChange={handleEditorChange}
        onMount={handleEditorDidMount}
        theme={theme === 'dark' ? 'vs-dark' : 'light'}
        options={{
          readOnly,
          automaticLayout: true,
          scrollBeyondLastLine: false,
        }}
      />
      <StatusBar>
        <StatusItem>字数: {wordCount.toLocaleString()}</StatusItem>
        <StatusItem>字符: {characterCount.toLocaleString()}</StatusItem>
        <StatusItem>{language === 'markdown' ? 'Markdown' : '纯文本'}</StatusItem>
        <StatusItem>{readOnly ? '只读' : '编辑'}</StatusItem>
      </StatusBar>
    </EditorContainer>
  )
}