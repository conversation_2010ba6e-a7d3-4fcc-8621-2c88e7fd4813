import 'styled-components'

declare module 'styled-components' {
  export interface DefaultTheme {
    colors: {
      primary: string
      primaryHover: string
      primaryDark: string
      secondary: string
      background: string
      surface: string
      border: string
      hover: string
      text: string
      textSecondary: string
      success: string
      warning: string
      error: string
      info: string
    }
    spacing: {
      xs: string
      sm: string
      md: string
      lg: string
      xl: string
      xxl: string
    }
    borderRadius: {
      sm: string
      md: string
      lg: string
      xl: string
    }
    shadows: {
      sm: string
      md: string
      lg: string
      xl: string
    }
    typography: {
      fontSize: string
      lineHeight: string
      fontFamily: string
    }
  }
}
