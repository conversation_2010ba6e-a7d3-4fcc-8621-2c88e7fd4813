/**
 * Text processing utilities for the novel creation manager
 */

/**
 * Count words in a text (Chinese characters and English words)
 */
export function countWords(text: string): number {
  if (!text.trim()) return 0

  // Count Chinese characters (including punctuation)
  const chineseChars = (text.match(/[\u4e00-\u9fff\u3000-\u303f\uff00-\uffef]/g) || [])
    .length

  // Count English words
  const englishWords = (text.match(/[a-zA-Z]+/g) || []).length

  return chineseChars + englishWords
}

/**
 * Count characters in a text
 */
export function countCharacters(text: string, includeSpaces: boolean = true): number {
  if (!includeSpaces) {
    return text.replace(/\s/g, '').length
  }
  return text.length
}

/**
 * Estimate reading time in minutes
 */
export function estimateReadingTime(
  text: string,
  wordsPerMinute: number = 300
): number {
  const wordCount = countWords(text)
  return Math.ceil(wordCount / wordsPerMinute)
}

/**
 * Extract keywords from text (simple implementation)
 */
export function extractKeywords(text: string, maxKeywords: number = 10): string[] {
  // Simple keyword extraction based on word frequency
  const words = text
    .toLowerCase()
    .replace(/[^\u4e00-\u9fff\w\s]/g, ' ')
    .split(/\s+/)
    .filter(word => word.length > 1)

  const wordFreq: Record<string, number> = {}
  words.forEach(word => {
    wordFreq[word] = (wordFreq[word] || 0) + 1
  })

  // Sort by frequency and return top keywords
  return Object.entries(wordFreq)
    .sort(([, a], [, b]) => b - a)
    .slice(0, maxKeywords)
    .map(([word]) => word)
}

/**
 * Truncate text to specified length with ellipsis
 */
export function truncateText(
  text: string,
  maxLength: number,
  ellipsis: string = '...'
): string {
  if (text.length <= maxLength) return text

  return text.substring(0, maxLength - ellipsis.length) + ellipsis
}

/**
 * Convert text to proper case for titles
 */
export function toTitleCase(text: string): string {
  return text.toLowerCase().replace(/(?:^|\s)\w/g, match => match.toUpperCase())
}

/**
 * Remove extra whitespace from text
 */
export function normalizeWhitespace(text: string): string {
  return text.replace(/\s+/g, ' ').trim()
}

/**
 * Check if text contains mostly Chinese characters
 */
export function isMostlyChinese(text: string): boolean {
  const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length
  const totalChars = text.replace(/\s/g, '').length

  if (totalChars === 0) return false

  return chineseChars / totalChars > 0.5
}
