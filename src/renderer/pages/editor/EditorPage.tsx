import React, { useEffect, useState, useCallback } from 'react'
import styled from 'styled-components'
import { useNovelStore } from '../../stores/novel'
import { Chapter } from '../../../shared/types'
import Editor from '../../components/editor/Editor'

const EditorContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: ${props => props.theme.colors.background};
`

const EditorHeader = styled.div`
  padding: ${props => props.theme.spacing.md};
  border-bottom: 1px solid ${props => props.theme.colors.border};
  background: ${props => props.theme.colors.surface};
  display: flex;
  align-items: center;
  justify-content: space-between;
`

const EditorTitle = styled.h1`
  font-size: 1.5rem;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0;
`

const EditorActions = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.sm};
  align-items: center;
`

const ExportMenu = styled.div<{ isOpen: boolean }>`
  display: ${props => props.isOpen ? 'block' : 'none'};
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 4px;
  background: ${props => props.theme.colors.surface};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  box-shadow: ${props => props.theme.shadows.md};
  z-index: 100;
`

const ExportMenuItem = styled.button`
  display: block;
  width: 100%;
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  text-align: left;
  border: none;
  background: transparent;
  color: ${props => props.theme.colors.text};
  cursor: pointer;
  
  &:hover {
    background: ${props => props.theme.colors.hover};
  }
`

const DropdownButton = styled.div`
  position: relative;
`

const EditorContent = styled.div`
  flex: 1;
  display: flex;
  overflow: hidden;
`

const Sidebar = styled.div`
  width: 250px;
  border-right: 1px solid ${props => props.theme.colors.border};
  background: ${props => props.theme.colors.surface};
  overflow-y: auto;
`

const MainEditor = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
`

const Button = styled.button`
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  border-radius: ${props => props.theme.borderRadius.md};
  background: ${props => props.theme.colors.primary};
  color: white;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;

  &:hover {
    background: ${props => props.theme.colors.primaryHover};
  }

  &:disabled {
    background: ${props => props.theme.colors.secondary};
    cursor: not-allowed;
  }
`

const SidebarHeader = styled.div`
  padding: ${props => props.theme.spacing.md};
  border-bottom: 1px solid ${props => props.theme.colors.border};
  display: flex;
  align-items: center;
  justify-content: space-between;
`

const SidebarTitle = styled.h3`
  font-size: 1rem;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0;
`

const ChapterList = styled.div`
  padding: ${props => props.theme.spacing.sm};
`

const ChapterItem = styled.div<{ $active?: boolean }>`
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  margin-bottom: ${props => props.theme.spacing.xs};
  border-radius: ${props => props.theme.borderRadius.sm};
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.2s ease;
  background: ${props => props.$active ? `${props.theme.colors.primary}20` : 'transparent'};
  color: ${props => props.$active ? props.theme.colors.primary : props.theme.colors.text};

  &:hover {
    background: ${props => props.$active ? `${props.theme.colors.primary}30` : props.theme.colors.hover};
  }
`

const ChapterInfo = styled.div`
  flex: 1;
  overflow: hidden;
`

const ChapterTitle = styled.div`
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`

const ChapterMeta = styled.div`
  font-size: 0.85rem;
  color: ${props => props.theme.colors.textSecondary};
  margin-top: 2px;
`

const ChapterActions = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.xs};
  opacity: 0;
  transition: opacity 0.2s ease;

  ${ChapterItem}:hover & {
    opacity: 1;
  }
`

const IconButton = styled.button`
  width: 24px;
  height: 24px;
  border-radius: 4px;
  border: none;
  background: transparent;
  color: ${props => props.theme.colors.textSecondary};
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;

  &:hover {
    background: ${props => props.theme.colors.border};
    color: ${props => props.theme.colors.text};
  }
`

const EmptyState = styled.div`
  padding: ${props => props.theme.spacing.lg};
  text-align: center;
  color: ${props => props.theme.colors.textSecondary};
`

const AddChapterButton = styled.button`
  width: 100%;
  padding: ${props => props.theme.spacing.sm};
  margin: ${props => props.theme.spacing.sm};
  border: 2px dashed ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.sm};
  background: transparent;
  color: ${props => props.theme.colors.textSecondary};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: ${props => props.theme.colors.primary};
    color: ${props => props.theme.colors.primary};
    background: ${props => props.theme.colors.primary}10;
  }
`

const EditorPage: React.FC = () => {
  const { 
    currentNovel, 
    chapters, 
    loadChapters, 
    createChapter, 
    deleteChapter 
  } = useNovelStore()
  const [currentChapter, setCurrentChapter] = useState<Chapter | null>(null)
  const [isCreatingChapter, setIsCreatingChapter] = useState(false)
  const [isExporting, setIsExporting] = useState(false)
  const [showExportMenu, setShowExportMenu] = useState(false)

  // 加载章节列表
  useEffect(() => {
    if (currentNovel?.id) {
      loadChapters(currentNovel.id)
    }
  }, [currentNovel?.id, loadChapters])

  // 自动选择第一个章节
  useEffect(() => {
    if (chapters.length > 0 && !currentChapter) {
      setCurrentChapter(chapters[0])
    }
  }, [chapters, currentChapter])

  // 创建新章节
  const handleNewChapter = useCallback(async () => {
    if (!currentNovel) return

    setIsCreatingChapter(true)
    try {
      const newChapter: Omit<Chapter, 'id' | 'createdAt' | 'updatedAt'> = {
        novelId: currentNovel.id,
        title: `第 ${chapters.length + 1} 章`,
        content: '',
        wordCount: 0,
        orderIndex: chapters.length,
        status: 'draft',
      }
      
      const chapterId = await createChapter(newChapter)
      const createdChapter = chapters.find(c => c.id === chapterId)
      if (createdChapter) {
        setCurrentChapter(createdChapter)
      }
    } catch (error) {
      console.error('Failed to create chapter:', error)
    } finally {
      setIsCreatingChapter(false)
    }
  }, [currentNovel, chapters, createChapter])

  // 删除章节
  const handleDeleteChapter = useCallback(async (chapterId: string, e: React.MouseEvent) => {
    e.stopPropagation()
    
    if (!window.confirm('确定要删除这个章节吗？')) {
      return
    }

    try {
      await deleteChapter(chapterId)
      if (currentChapter?.id === chapterId) {
        setCurrentChapter(chapters.find(c => c.id !== chapterId) || null)
      }
    } catch (error) {
      console.error('Failed to delete chapter:', error)
    }
  }, [currentChapter, chapters, deleteChapter])

  // 章节更新回调
  const handleChapterUpdate = useCallback((updatedChapter: Chapter) => {
    // 更新当前章节状态
    setCurrentChapter(updatedChapter)
  }, [])

  // 格式化字数
  const formatWordCount = (count: number) => {
    if (count < 1000) return `${count} 字`
    return `${(count / 1000).toFixed(1)}k 字`
  }

  // 导出整部小说
  const handleExportNovel = useCallback(async (format: 'markdown' | 'txt' | 'html') => {
    if (!currentNovel || chapters.length === 0) {
      alert('没有可导出的内容')
      return
    }

    setIsExporting(true)
    try {
      let content = ''
      
      // 添加小说信息
      if (format === 'markdown') {
        content = `# ${currentNovel.title}\n\n`
        content += `作者: ${currentNovel.author || '佚名'}\n\n`
        content += `简介: ${currentNovel.description || '暂无简介'}\n\n`
        content += `---\n\n`
        
        // 添加所有章节
        chapters.forEach((chapter, index) => {
          content += `## 第${index + 1}章 ${chapter.title}\n\n`
          content += `${chapter.content}\n\n`
        })
      } else if (format === 'txt') {
        content = `${currentNovel.title}\n\n`
        content += `作者: ${currentNovel.author || '佚名'}\n\n`
        content += `简介: ${currentNovel.description || '暂无简介'}\n\n`
        content += `${'='.repeat(50)}\n\n`
        
        chapters.forEach((chapter, index) => {
          content += `第${index + 1}章 ${chapter.title}\n\n`
          content += `${chapter.content}\n\n`
          content += `${'='.repeat(50)}\n\n`
        })
      } else if (format === 'html') {
        content = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>${currentNovel.title}</title>
  <style>
    body { font-family: 'PingFang SC', sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
    h1 { text-align: center; }
    h2 { margin-top: 30px; }
    p { line-height: 1.8; text-indent: 2em; }
    .info { text-align: center; color: #666; }
  </style>
</head>
<body>
  <h1>${currentNovel.title}</h1>
  <div class="info">
    <p>作者: ${currentNovel.author || '佚名'}</p>
    <p>${currentNovel.description || '暂无简介'}</p>
  </div>
  <hr>`
        
        chapters.forEach((chapter, index) => {
          content += `\n  <h2>第${index + 1}章 ${chapter.title}</h2>\n`
          content += `  <div>${chapter.content.split('\n').map(p => p ? `<p>${p}</p>` : '').join('\n  ')}</div>\n`
        })
        
        content += `\n</body>\n</html>`
      }

      // 创建下载
      const blob = new Blob([content], { 
        type: format === 'html' ? 'text/html' : format === 'markdown' ? 'text/markdown' : 'text/plain' 
      })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${currentNovel.title}.${format === 'markdown' ? 'md' : format}`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('导出失败:', error)
      alert('导出失败，请重试')
    } finally {
      setIsExporting(false)
    }
  }, [currentNovel, chapters])

  if (!currentNovel) {
    return (
      <EditorContainer>
        <EditorContent style={{ alignItems: 'center', justifyContent: 'center' }}>
          <EmptyState>
            <h2>欢迎使用小说创作管理器</h2>
            <p>请先创建或打开一本小说开始写作</p>
          </EmptyState>
        </EditorContent>
      </EditorContainer>
    )
  }

  return (
    <EditorContainer>
      <EditorHeader>
        <EditorTitle>{currentNovel.title}</EditorTitle>
        <EditorActions>
          <span style={{ marginRight: '16px', color: '#666' }}>
            共 {chapters.length} 章 • {currentNovel.wordCount.toLocaleString()} 字
          </span>
          <DropdownButton>
            <Button 
              onClick={() => setShowExportMenu(!showExportMenu)}
              disabled={isExporting || chapters.length === 0}
            >
              {isExporting ? '导出中...' : '📥 导出小说'}
            </Button>
            <ExportMenu isOpen={showExportMenu}>
              <ExportMenuItem onClick={() => {
                handleExportNovel('markdown')
                setShowExportMenu(false)
              }}>
                📝 Markdown (.md)
              </ExportMenuItem>
              <ExportMenuItem onClick={() => {
                handleExportNovel('txt')
                setShowExportMenu(false)
              }}>
                📄 纯文本 (.txt)
              </ExportMenuItem>
              <ExportMenuItem onClick={() => {
                handleExportNovel('html')
                setShowExportMenu(false)
              }}>
                🌐 HTML (.html)
              </ExportMenuItem>
            </ExportMenu>
          </DropdownButton>
        </EditorActions>
      </EditorHeader>

      <EditorContent>
        <Sidebar>
          <SidebarHeader>
            <SidebarTitle>章节列表</SidebarTitle>
            <IconButton onClick={handleNewChapter} disabled={isCreatingChapter} title="新建章节">
              ➕
            </IconButton>
          </SidebarHeader>
          
          {chapters.length > 0 ? (
            <ChapterList>
              {chapters.map(chapter => (
                <ChapterItem
                  key={chapter.id}
                  $active={currentChapter?.id === chapter.id}
                  onClick={() => setCurrentChapter(chapter)}
                >
                  <ChapterInfo>
                    <ChapterTitle>{chapter.title || '未命名章节'}</ChapterTitle>
                    <ChapterMeta>
                      {formatWordCount(chapter.wordCount)} • {chapter.status === 'draft' ? '草稿' : '已完成'}
                    </ChapterMeta>
                  </ChapterInfo>
                  <ChapterActions>
                    <IconButton 
                      onClick={(e) => handleDeleteChapter(chapter.id, e)}
                      title="删除章节"
                    >
                      🗑️
                    </IconButton>
                  </ChapterActions>
                </ChapterItem>
              ))}
              <AddChapterButton onClick={handleNewChapter} disabled={isCreatingChapter}>
                {isCreatingChapter ? '创建中...' : '+ 添加新章节'}
              </AddChapterButton>
            </ChapterList>
          ) : (
            <EmptyState>
              <p>还没有章节</p>
              <Button onClick={handleNewChapter} disabled={isCreatingChapter}>
                {isCreatingChapter ? '创建中...' : '创建第一章'}
              </Button>
            </EmptyState>
          )}
        </Sidebar>

        <MainEditor>
          <Editor 
            chapter={currentChapter} 
            onUpdate={handleChapterUpdate}
          />
        </MainEditor>
      </EditorContent>
    </EditorContainer>
  )
}

export default EditorPage
