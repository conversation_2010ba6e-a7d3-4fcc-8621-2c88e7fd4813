import React, { useState, useCallback } from 'react'
import styled from 'styled-components'
import { useNavigate } from 'react-router-dom'
import { useNovelStore } from '../../stores/novel'
import { Novel } from '../../../shared/types'

const DashboardContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: ${props => props.theme.colors.background};
`

const Header = styled.div`
  padding: ${props => props.theme.spacing.lg};
  background: ${props => props.theme.colors.surface};
  border-bottom: 1px solid ${props => props.theme.colors.border};
`

const HeaderContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
`

const Title = styled.h1`
  font-size: 2rem;
  color: ${props => props.theme.colors.text};
  margin: 0;
`

const ActionButtons = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.md};
`

const Button = styled.button<{ variant?: 'primary' | 'secondary' }>`
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.lg};
  border-radius: ${props => props.theme.borderRadius.md};
  border: none;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  background: ${props =>
    props.variant === 'primary' ? props.theme.colors.primary : props.theme.colors.secondary};
  color: white;

  &:hover {
    background: ${props =>
      props.variant === 'primary' ? props.theme.colors.primaryHover : props.theme.colors.secondary};
    transform: translateY(-1px);
    box-shadow: ${props => props.theme.shadows.md};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`

const Content = styled.div`
  flex: 1;
  padding: ${props => props.theme.spacing.lg};
  overflow-y: auto;
`

const NovelGrid = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: ${props => props.theme.spacing.lg};
`

const NovelCard = styled.div`
  background: ${props => props.theme.colors.surface};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.lg};
  padding: ${props => props.theme.spacing.lg};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${props => props.theme.shadows.lg};
    border-color: ${props => props.theme.colors.primary};
  }
`

const NovelCover = styled.div`
  width: 100%;
  height: 150px;
  background: linear-gradient(135deg, ${props => props.theme.colors.primary}20, ${props => props.theme.colors.primary}40);
  border-radius: ${props => props.theme.borderRadius.md};
  margin-bottom: ${props => props.theme.spacing.md};
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
`

const NovelTitle = styled.h3`
  font-size: 1.2rem;
  color: ${props => props.theme.colors.text};
  margin: 0 0 ${props => props.theme.spacing.sm} 0;
`

const NovelInfo = styled.div`
  color: ${props => props.theme.colors.textSecondary};
  font-size: 0.9rem;
  line-height: 1.5;
`

const NovelStats = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.md};
  margin-top: ${props => props.theme.spacing.md};
  padding-top: ${props => props.theme.spacing.md};
  border-top: 1px solid ${props => props.theme.colors.border};
`

const Stat = styled.div`
  display: flex;
  flex-direction: column;
`

const StatLabel = styled.span`
  font-size: 0.8rem;
  color: ${props => props.theme.colors.textSecondary};
`

const StatValue = styled.span`
  font-size: 1rem;
  color: ${props => props.theme.colors.text};
  font-weight: 600;
`

const EmptyState = styled.div`
  text-align: center;
  padding: ${props => props.theme.spacing.xxl};
  color: ${props => props.theme.colors.textSecondary};
`

const EmptyIcon = styled.div`
  font-size: 4rem;
  margin-bottom: ${props => props.theme.spacing.lg};
`

const EmptyTitle = styled.h2`
  font-size: 1.5rem;
  color: ${props => props.theme.colors.text};
  margin-bottom: ${props => props.theme.spacing.md};
`

const EmptyDescription = styled.p`
  font-size: 1rem;
  margin-bottom: ${props => props.theme.spacing.lg};
`

const Modal = styled.div<{ isOpen: boolean }>`
  display: ${props => props.isOpen ? 'flex' : 'none'};
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  align-items: center;
  justify-content: center;
  z-index: 1000;
`

const ModalContent = styled.div`
  background: ${props => props.theme.colors.surface};
  border-radius: ${props => props.theme.borderRadius.lg};
  padding: ${props => props.theme.spacing.lg};
  width: 90%;
  max-width: 500px;
  box-shadow: ${props => props.theme.shadows.xl};
`

const ModalHeader = styled.h2`
  margin: 0 0 ${props => props.theme.spacing.lg} 0;
  color: ${props => props.theme.colors.text};
`

const FormGroup = styled.div`
  margin-bottom: ${props => props.theme.spacing.md};
`

const Label = styled.label`
  display: block;
  margin-bottom: ${props => props.theme.spacing.xs};
  color: ${props => props.theme.colors.text};
  font-weight: 500;
`

const Input = styled.input`
  width: 100%;
  padding: ${props => props.theme.spacing.sm};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.sm};
  background: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
  font-size: 1rem;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }
`

const TextArea = styled.textarea`
  width: 100%;
  padding: ${props => props.theme.spacing.sm};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.sm};
  background: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
  font-size: 1rem;
  resize: vertical;
  min-height: 100px;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }
`

const ModalFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: ${props => props.theme.spacing.md};
  margin-top: ${props => props.theme.spacing.lg};
`

const DashboardPage: React.FC = () => {
  const navigate = useNavigate()
  const { novels, createNovel, setCurrentNovel } = useNovelStore()
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [newNovel, setNewNovel] = useState({
    title: '',
    author: '',
    description: '',
    genre: [] as string[],
    tags: [] as string[],
  })

  const handleCreateNovel = useCallback(async () => {
    if (!newNovel.title.trim()) {
      alert('请输入小说标题')
      return
    }

    try {
      const novelId = await createNovel({
        ...newNovel,
        status: 'draft',
        wordCount: 0,
        chapterCount: 0,
      })

      // 找到新创建的小说并设置为当前小说
      const createdNovel = novels.find(n => n.id === novelId)
      if (createdNovel) {
        setCurrentNovel(createdNovel)
        navigate('/editor')
      }

      setIsCreateModalOpen(false)
      setNewNovel({
        title: '',
        author: '',
        description: '',
        genre: [],
        tags: [],
      })
    } catch (error) {
      console.error('Failed to create novel:', error)
      alert('创建小说失败，请重试')
    }
  }, [newNovel, createNovel, navigate, novels, setCurrentNovel])

  const handleOpenNovel = useCallback((novel: Novel) => {
    setCurrentNovel(novel)
    navigate('/editor')
  }, [navigate, setCurrentNovel])

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    })
  }

  const formatWordCount = (count: number) => {
    if (count < 10000) return `${count}字`
    return `${(count / 10000).toFixed(1)}万字`
  }

  return (
    <DashboardContainer>
      <Header>
        <HeaderContent>
          <Title>我的小说</Title>
          <ActionButtons>
            <Button variant="primary" onClick={() => setIsCreateModalOpen(true)}>
              ➕ 新建小说
            </Button>
            <Button variant="secondary">
              📥 导入小说
            </Button>
          </ActionButtons>
        </HeaderContent>
      </Header>

      <Content>
        {novels.length > 0 ? (
          <NovelGrid>
            {novels.map(novel => (
              <NovelCard key={novel.id} onClick={() => handleOpenNovel(novel)}>
                <NovelCover>📚</NovelCover>
                <NovelTitle>{novel.title}</NovelTitle>
                <NovelInfo>
                  作者：{novel.author || '未设置'}
                  <br />
                  {novel.description || '暂无简介'}
                </NovelInfo>
                <NovelStats>
                  <Stat>
                    <StatLabel>字数</StatLabel>
                    <StatValue>{formatWordCount(novel.wordCount)}</StatValue>
                  </Stat>
                  <Stat>
                    <StatLabel>章节</StatLabel>
                    <StatValue>{novel.chapterCount}章</StatValue>
                  </Stat>
                  <Stat>
                    <StatLabel>更新</StatLabel>
                    <StatValue>{formatDate(novel.updatedAt)}</StatValue>
                  </Stat>
                </NovelStats>
              </NovelCard>
            ))}
          </NovelGrid>
        ) : (
          <EmptyState>
            <EmptyIcon>📚</EmptyIcon>
            <EmptyTitle>开始你的创作之旅</EmptyTitle>
            <EmptyDescription>
              还没有任何小说，点击下方按钮创建你的第一部作品
            </EmptyDescription>
            <Button variant="primary" onClick={() => setIsCreateModalOpen(true)}>
              ➕ 创建第一部小说
            </Button>
          </EmptyState>
        )}
      </Content>

      <Modal isOpen={isCreateModalOpen}>
        <ModalContent>
          <ModalHeader>创建新小说</ModalHeader>
          
          <FormGroup>
            <Label>小说标题 *</Label>
            <Input
              type="text"
              value={newNovel.title}
              onChange={e => setNewNovel({ ...newNovel, title: e.target.value })}
              placeholder="输入小说标题..."
              autoFocus
            />
          </FormGroup>

          <FormGroup>
            <Label>作者</Label>
            <Input
              type="text"
              value={newNovel.author}
              onChange={e => setNewNovel({ ...newNovel, author: e.target.value })}
              placeholder="输入作者名称..."
            />
          </FormGroup>

          <FormGroup>
            <Label>简介</Label>
            <TextArea
              value={newNovel.description}
              onChange={e => setNewNovel({ ...newNovel, description: e.target.value })}
              placeholder="输入小说简介..."
            />
          </FormGroup>

          <ModalFooter>
            <Button variant="secondary" onClick={() => setIsCreateModalOpen(false)}>
              取消
            </Button>
            <Button variant="primary" onClick={handleCreateNovel}>
              创建
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </DashboardContainer>
  )
}

export default DashboardPage