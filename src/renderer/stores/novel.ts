import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { Novel, Chapter, Character, Outline, Material } from '../../shared/types'

interface NovelState {
  // 数据状态
  novels: Novel[]
  currentNovel: Novel | null
  chapters: Chapter[]
  characters: Character[]
  outlines: Outline[]
  materials: Material[]

  // UI状态
  loading: boolean
  error: string | null

  // 操作方法
  // 小说相关
  loadNovels: () => Promise<void>
  createNovel: (novel: Omit<Novel, 'id' | 'createdAt' | 'updatedAt'>) => Promise<string>
  updateNovel: (id: string, updates: Partial<Novel>) => Promise<void>
  deleteNovel: (id: string) => Promise<void>
  setCurrentNovel: (novel: Novel | null) => void

  // 章节相关
  loadChapters: (novelId: string) => Promise<void>
  createChapter: (
    chapter: Omit<Chapter, 'id' | 'createdAt' | 'updatedAt'>
  ) => Promise<string>
  updateChapter: (id: string, updates: Partial<Chapter>) => Promise<void>
  deleteChapter: (id: string) => Promise<void>
  reorderChapters: (chapterIds: string[]) => Promise<void>

  // 角色相关
  loadCharacters: (novelId: string) => Promise<void>
  createCharacter: (
    character: Omit<Character, 'id' | 'createdAt' | 'updatedAt'>
  ) => Promise<string>
  updateCharacter: (id: string, updates: Partial<Character>) => Promise<void>
  deleteCharacter: (id: string) => Promise<void>

  // 大纲相关
  loadOutlines: (novelId: string) => Promise<void>
  createOutline: (
    outline: Omit<Outline, 'id' | 'createdAt' | 'updatedAt'>
  ) => Promise<string>
  updateOutline: (id: string, updates: Partial<Outline>) => Promise<void>
  deleteOutline: (id: string) => Promise<void>

  // 素材相关
  loadMaterials: (novelId?: string) => Promise<void>
  createMaterial: (
    material: Omit<Material, 'id' | 'createdAt' | 'updatedAt'>
  ) => Promise<string>
  updateMaterial: (id: string, updates: Partial<Material>) => Promise<void>
  deleteMaterial: (id: string) => Promise<void>

  // 工具方法
  clearError: () => void
  setLoading: (loading: boolean) => void
}

export const useNovelStore = create<NovelState>()(
  devtools(
    persist(
      set => ({
        // 初始状态
        novels: [],
        currentNovel: null,
        chapters: [],
        characters: [],
        outlines: [],
        materials: [],
        loading: false,
        error: null,

        // 小说操作
        loadNovels: async () => {
          try {
            set({ loading: true, error: null })

            // 通过IPC调用主进程的数据库操作
            // @ts-ignore
            const novels = await window.electron.databaseQuery(
              'SELECT * FROM novels ORDER BY updated_at DESC'
            )

            set({
              novels: novels.map((novel: any) => ({
                ...novel,
                genre: JSON.parse(novel.genre || '[]'),
                tags: JSON.parse(novel.tags || '[]'),
                createdAt: new Date(novel.created_at),
                updatedAt: new Date(novel.updated_at),
              })),
              loading: false,
            })
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : '加载小说列表失败',
              loading: false,
            })
          }
        },

        createNovel: async novelData => {
          try {
            set({ loading: true, error: null })

            const id = `novel_${Date.now()}`
            const novel = {
              ...novelData,
              id,
              genre: JSON.stringify(novelData.genre || []),
              tags: JSON.stringify(novelData.tags || []),
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            }

            // @ts-ignore
            await window.electron.databaseRun(
              `INSERT INTO novels (id, title, author, description, cover_image, word_count, chapter_count, status, genre, tags, created_at, updated_at) 
               VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
              [
                novel.id,
                novel.title,
                novel.author,
                novel.description,
                novel.coverImage,
                novel.wordCount,
                novel.chapterCount,
                novel.status,
                novel.genre,
                novel.tags,
                novel.created_at,
                novel.updated_at,
              ]
            )

            const newNovel: Novel = {
              ...novelData,
              id,
              createdAt: new Date(novel.created_at),
              updatedAt: new Date(novel.updated_at),
            }

            set(state => ({
              novels: [newNovel, ...state.novels],
              loading: false,
            }))

            return id
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : '创建小说失败',
              loading: false,
            })
            throw error
          }
        },

        updateNovel: async (id, updates) => {
          try {
            set({ loading: true, error: null })

            const updateData = {
              ...updates,
              genre: JSON.stringify(updates.genre || []),
              tags: JSON.stringify(updates.tags || []),
              updated_at: new Date().toISOString(),
            }

            // @ts-ignore
            await window.electron.databaseRun(
              `UPDATE novels SET title = ?, author = ?, description = ?, cover_image = ?, word_count = ?, chapter_count = ?, status = ?, genre = ?, tags = ?, updated_at = ? WHERE id = ?`,
              [
                updateData.title,
                updateData.author,
                updateData.description,
                updateData.coverImage,
                updateData.wordCount,
                updateData.chapterCount,
                updateData.status,
                updateData.genre,
                updateData.tags,
                updateData.updated_at,
                id,
              ]
            )

            set(state => ({
              novels: state.novels.map(novel =>
                novel.id === id
                  ? {
                      ...novel,
                      ...updates,
                      updatedAt: new Date(updateData.updated_at),
                    }
                  : novel
              ),
              currentNovel:
                state.currentNovel?.id === id
                  ? {
                      ...state.currentNovel,
                      ...updates,
                      updatedAt: new Date(updateData.updated_at),
                    }
                  : state.currentNovel,
              loading: false,
            }))
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : '更新小说失败',
              loading: false,
            })
            throw error
          }
        },

        deleteNovel: async id => {
          try {
            set({ loading: true, error: null })

            // @ts-ignore
            await window.electron.databaseRun('DELETE FROM novels WHERE id = ?', [id])

            set(state => ({
              novels: state.novels.filter(novel => novel.id !== id),
              currentNovel: state.currentNovel?.id === id ? null : state.currentNovel,
              loading: false,
            }))
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : '删除小说失败',
              loading: false,
            })
            throw error
          }
        },

        setCurrentNovel: novel => {
          set({ currentNovel: novel })
        },

        // 章节操作
        loadChapters: async novelId => {
          try {
            set({ loading: true, error: null })

            // @ts-ignore
            const chapters = await window.electron.databaseQuery(
              'SELECT * FROM chapters WHERE novel_id = ? ORDER BY order_index',
              [novelId]
            )

            set({
              chapters: chapters.map((chapter: any) => ({
                ...chapter,
                createdAt: new Date(chapter.created_at),
                updatedAt: new Date(chapter.updated_at),
              })),
              loading: false,
            })
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : '加载章节失败',
              loading: false,
            })
          }
        },

        createChapter: async chapterData => {
          try {
            set({ loading: true, error: null })

            const id = `chapter_${Date.now()}`
            const chapter = {
              ...chapterData,
              id,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            }

            // @ts-ignore
            await window.electron.databaseRun(
              `INSERT INTO chapters (id, novel_id, title, content, word_count, order_index, status, created_at, updated_at) 
               VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
              [
                chapter.id,
                chapter.novelId,
                chapter.title,
                chapter.content,
                chapter.wordCount,
                chapter.orderIndex,
                chapter.status,
                chapter.created_at,
                chapter.updated_at,
              ]
            )

            const newChapter: Chapter = {
              ...chapterData,
              id,
              createdAt: new Date(chapter.created_at),
              updatedAt: new Date(chapter.updated_at),
            }

            set(state => ({
              chapters: [...state.chapters, newChapter],
              loading: false,
            }))

            return id
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : '创建章节失败',
              loading: false,
            })
            throw error
          }
        },

        updateChapter: async (id, updates) => {
          try {
            set({ loading: true, error: null })

            const updateData = {
              ...updates,
              updated_at: new Date().toISOString(),
            }

            // @ts-ignore
            await window.electron.databaseRun(
              `UPDATE chapters SET title = ?, content = ?, word_count = ?, status = ?, updated_at = ? WHERE id = ?`,
              [
                updateData.title,
                updateData.content,
                updateData.wordCount,
                updateData.status,
                updateData.updated_at,
                id,
              ]
            )

            set(state => ({
              chapters: state.chapters.map(chapter =>
                chapter.id === id
                  ? {
                      ...chapter,
                      ...updates,
                      updatedAt: new Date(updateData.updated_at),
                    }
                  : chapter
              ),
              loading: false,
            }))
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : '更新章节失败',
              loading: false,
            })
            throw error
          }
        },

        deleteChapter: async id => {
          try {
            set({ loading: true, error: null })

            // @ts-ignore
            await window.electron.databaseRun('DELETE FROM chapters WHERE id = ?', [id])

            set(state => ({
              chapters: state.chapters.filter(chapter => chapter.id !== id),
              loading: false,
            }))
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : '删除章节失败',
              loading: false,
            })
            throw error
          }
        },

        // 其他方法的简化实现...
        loadCharacters: async () => {
          try {
            set({ loading: true, error: null })
            // 实现加载角色逻辑
            set({ loading: false })
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : '加载角色失败',
              loading: false,
            })
          }
        },

        createCharacter: async () => {
          try {
            set({ loading: true, error: null })
            // 实现创建角色逻辑
            set({ loading: false })
            return 'character_id'
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : '创建角色失败',
              loading: false,
            })
            throw error
          }
        },

        updateCharacter: async () => {
          try {
            set({ loading: true, error: null })
            // 实现更新角色逻辑
            set({ loading: false })
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : '更新角色失败',
              loading: false,
            })
            throw error
          }
        },

        deleteCharacter: async () => {
          try {
            set({ loading: true, error: null })
            // 实现删除角色逻辑
            set({ loading: false })
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : '删除角色失败',
              loading: false,
            })
            throw error
          }
        },

        // 简化其他方法实现...
        loadOutlines: async () => {},
        createOutline: async () => {
          return 'outline_id'
        },
        updateOutline: async () => {},
        deleteOutline: async () => {},
        loadMaterials: async () => {},
        createMaterial: async () => {
          return 'material_id'
        },
        updateMaterial: async () => {},
        deleteMaterial: async () => {},
        reorderChapters: async () => {},

        // 工具方法
        clearError: () => set({ error: null }),
        setLoading: loading => set({ loading }),
      }),
      {
        name: 'novel-store',
        partialize: state => ({
          currentNovel: state.currentNovel,
          novels: state.novels,
        }),
      }
    ),
    { name: 'novel-store' }
  )
)
