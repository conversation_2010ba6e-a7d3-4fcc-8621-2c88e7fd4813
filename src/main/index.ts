import { app, BrowserWindow, ipc<PERSON><PERSON>, <PERSON>u, dialog } from 'electron'
import path from 'path'
import { DatabaseService } from './database/DatabaseService'
import { AIService } from './ai/AIService'
import { StorageService } from './storage/StorageService'
import { NovelService, SettingsService } from './services'
import {
  AIRequest,
  Chapter,
  Character,
  Material,
  Novel,
  Outline,
  AIServiceConfig,
  AppSettings,
} from '../shared/types'

class NovelCreationManager {
  private mainWindow: BrowserWindow | null = null
  private databaseService: DatabaseService
  private aiService: AIService
  private storageService: StorageService
  private novelService: NovelService
  private settingsService: SettingsService

  constructor() {
    this.databaseService = new DatabaseService()
    this.aiService = new AIService()
    this.storageService = new StorageService(this.databaseService)
    this.novelService = new NovelService(this.databaseService)
    this.settingsService = new SettingsService(this.databaseService)

    this.setupApp()
    this.setupIPC()
  }

  private setupApp() {
    // 禁用硬件加速以避免一些兼容性问题
    app.disableHardwareAcceleration()

    // 当所有窗口关闭时退出应用
    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit()
      }
    })

    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        this.createWindow()
      }
    })

    app.whenReady().then(() => {
      this.createWindow()
      this.setupMenu()
    })
  }

  private createWindow() {
    this.mainWindow = new BrowserWindow({
      width: 1400,
      height: 900,
      minWidth: 800,
      minHeight: 600,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, '../preload/index.js'),
        webSecurity: true,
        allowRunningInsecureContent: false,
      },
      titleBarStyle: 'default',
      show: false,
    })

    // 开发环境下加载开发服务器
    if (process.env.NODE_ENV === 'development') {
      this.mainWindow.loadURL('http://localhost:3000')
      this.mainWindow.webContents.openDevTools()
    } else {
      this.mainWindow.loadFile(path.join(__dirname, 'renderer/index.html'))
    }

    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow?.show()
    })

    this.mainWindow.on('closed', () => {
      this.mainWindow = null
    })
  }

  private setupMenu() {
    const template: Electron.MenuItemConstructorOptions[] = [
      {
        label: '文件',
        submenu: [
          {
            label: '新建小说',
            accelerator: 'CmdOrCtrl+N',
            click: () => this.handleNewNovel(),
          },
          {
            label: '打开小说',
            accelerator: 'CmdOrCtrl+O',
            click: () => this.handleOpenNovel(),
          },
          { type: 'separator' },
          {
            label: '保存',
            accelerator: 'CmdOrCtrl+S',
            click: () => this.handleSave(),
          },
          {
            label: '导出',
            submenu: [
              {
                label: '导出为DOC',
                click: () => this.handleExport('doc'),
              },
              {
                label: '导出为Markdown',
                click: () => this.handleExport('markdown'),
              },
              {
                label: '导出为TXT',
                click: () => this.handleExport('txt'),
              },
            ],
          },
          { type: 'separator' },
          {
            label: '退出',
            accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
            click: () => app.quit(),
          },
        ],
      },
      {
        label: '编辑',
        submenu: [
          {
            label: '撤销',
            accelerator: 'CmdOrCtrl+Z',
            role: 'undo',
          },
          {
            label: '重做',
            accelerator: 'CmdOrCtrl+Y',
            role: 'redo',
          },
          { type: 'separator' },
          {
            label: '剪切',
            accelerator: 'CmdOrCtrl+X',
            role: 'cut',
          },
          {
            label: '复制',
            accelerator: 'CmdOrCtrl+C',
            role: 'copy',
          },
          {
            label: '粘贴',
            accelerator: 'CmdOrCtrl+V',
            role: 'paste',
          },
          {
            label: '全选',
            accelerator: 'CmdOrCtrl+A',
            role: 'selectAll',
          },
        ],
      },
      {
        label: '视图',
        submenu: [
          {
            label: '重新加载',
            accelerator: 'CmdOrCtrl+R',
            role: 'reload',
          },
          {
            label: '强制重新加载',
            accelerator: 'CmdOrCtrl+Shift+R',
            role: 'forceReload',
          },
          {
            label: '开发者工具',
            accelerator: process.platform === 'darwin' ? 'Alt+Cmd+I' : 'Ctrl+Shift+I',
            role: 'toggleDevTools',
          },
          { type: 'separator' },
          {
            label: '实际大小',
            accelerator: 'CmdOrCtrl+0',
            role: 'resetZoom',
          },
          {
            label: '放大',
            accelerator: 'CmdOrCtrl+Plus',
            role: 'zoomIn',
          },
          {
            label: '缩小',
            accelerator: 'CmdOrCtrl+-',
            role: 'zoomOut',
          },
          { type: 'separator' },
          {
            label: '全屏',
            accelerator: process.platform === 'darwin' ? 'Ctrl+Cmd+F' : 'F11',
            role: 'togglefullscreen',
          },
        ],
      },
      {
        label: '工具',
        submenu: [
          {
            label: 'AI助手',
            accelerator: 'CmdOrCtrl+Shift+A',
            click: () => this.handleAIAssistant(),
          },
          {
            label: '字数统计',
            accelerator: 'CmdOrCtrl+Shift+W',
            click: () => this.handleWordCount(),
          },
          {
            label: '小黑屋模式',
            accelerator: 'CmdOrCtrl+Shift+F',
            click: () => this.handleDistractionFreeMode(),
          },
        ],
      },
      {
        label: '帮助',
        submenu: [
          {
            label: '关于',
            click: () => this.handleAbout(),
          },
          {
            label: '检查更新',
            click: () => this.handleCheckUpdate(),
          },
        ],
      },
    ]

    const menu = Menu.buildFromTemplate(template)
    Menu.setApplicationMenu(menu)
  }

  private setupIPC() {
    // 数据库操作
    ipcMain.handle(
      'database:query',
      async (_event, sql: string, params: (string | number | null)[]) => {
        return await this.databaseService.query(sql, params)
      }
    )

    ipcMain.handle(
      'database:run',
      async (_event, sql: string, params: (string | number | null)[]) => {
        return await this.databaseService.run(sql, params)
      }
    )

    // AI服务
    ipcMain.handle('ai:generate', async (_event, request: AIRequest) => {
      return await this.aiService.generate(request)
    })

    // 小说服务
    ipcMain.handle('novel:create', async (_event, novel: Novel) => {
      return await this.novelService.createNovel(novel)
    })

    ipcMain.handle('novel:get', async (_event, id: string) => {
      return await this.novelService.getNovel(id)
    })

    ipcMain.handle('novel:getAll', async () => {
      return await this.novelService.getAllNovels()
    })

    ipcMain.handle(
      'novel:update',
      async (_event, id: string, updates: Partial<Novel>) => {
        return await this.novelService.updateNovel(id, updates)
      }
    )

    ipcMain.handle('novel:delete', async (_event, id: string) => {
      return await this.novelService.deleteNovel(id)
    })

    // 章节服务
    ipcMain.handle('chapter:create', async (_event, chapter: Chapter) => {
      return await this.novelService.createChapter(chapter)
    })

    ipcMain.handle('chapter:getByNovel', async (_event, novelId: string) => {
      return await this.novelService.getChaptersByNovelId(novelId)
    })

    ipcMain.handle(
      'chapter:update',
      async (_event, id: string, updates: Partial<Chapter>) => {
        return await this.novelService.updateChapter(id, updates)
      }
    )

    ipcMain.handle('chapter:delete', async (_event, id: string) => {
      return await this.novelService.deleteChapter(id)
    })

    // 角色服务
    ipcMain.handle('character:create', async (_event, character: Character) => {
      return await this.novelService.createCharacter(character)
    })

    ipcMain.handle('character:getByNovel', async (_event, novelId: string) => {
      return await this.novelService.getCharactersByNovelId(novelId)
    })

    ipcMain.handle(
      'character:update',
      async (_event, id: string, updates: Partial<Character>) => {
        return await this.novelService.updateCharacter(id, updates)
      }
    )

    ipcMain.handle('character:delete', async (_event, id: string) => {
      return await this.novelService.deleteCharacter(id)
    })

    // 素材服务
    ipcMain.handle('material:create', async (_event, material: Material) => {
      return await this.novelService.createMaterial(material)
    })

    ipcMain.handle('material:getAll', async () => {
      return await this.novelService.getAllMaterials()
    })

    ipcMain.handle('material:getByNovel', async (_event, novelId: string) => {
      return await this.novelService.getMaterialsByNovelId(novelId)
    })

    ipcMain.handle(
      'material:update',
      async (_event, id: string, updates: Partial<Material>) => {
        return await this.novelService.updateMaterial(id, updates)
      }
    )

    ipcMain.handle('material:delete', async (_event, id: string) => {
      return await this.novelService.deleteMaterial(id)
    })

    // 大纲服务
    ipcMain.handle('outline:create', async (_event, outline: Outline) => {
      return await this.novelService.createOutline(outline)
    })

    ipcMain.handle('outline:getByNovel', async (_event, novelId: string) => {
      return await this.novelService.getOutlinesByNovelId(novelId)
    })

    ipcMain.handle(
      'outline:update',
      async (_event, id: string, updates: Partial<Outline>) => {
        return await this.novelService.updateOutline(id, updates)
      }
    )

    ipcMain.handle('outline:delete', async (_event, id: string) => {
      return await this.novelService.deleteOutline(id)
    })

    // 设置服务
    ipcMain.handle('settings:get', async () => {
      return await this.settingsService.getSettings()
    })

    ipcMain.handle('settings:update', async (_event, updates: Partial<AppSettings>) => {
      return await this.settingsService.updateSettings(updates)
    })

    ipcMain.handle('settings:getAIConfig', async (_event, provider: string) => {
      return await this.settingsService.getAIServiceConfig(provider)
    })

    ipcMain.handle('settings:saveAIConfig', async (_event, config: AIServiceConfig) => {
      return await this.settingsService.saveAIServiceConfig(config)
    })

    // 文件操作
    ipcMain.handle('file:save', async (_event, data: string, path: string) => {
      return await this.storageService.saveFile(data, path)
    })

    ipcMain.handle('file:read', async (_event, path: string) => {
      return await this.storageService.readFile(path)
    })

    // 导出功能
    ipcMain.handle(
      'export:novel',
      async (_event, novelId: string, options: { format: string }) => {
        return await this.storageService.exportNovel(novelId, options)
      }
    )
  }

  // 菜单事件处理器
  private async handleNewNovel() {}

  private async handleOpenNovel() {}

  private handleSave() {}

  private async handleExport(_format: string) {}

  private handleAIAssistant() {}

  private handleWordCount() {}

  private handleDistractionFreeMode() {}

  private handleAbout() {
    dialog.showMessageBox(this.mainWindow!, {
      title: '关于小说创作管理器',
      message: '小说创作管理器',
      detail: '版本 1.0.0\n一款专为小说创作者设计的智能写作助手',
      buttons: ['确定'],
    })
  }

  private handleCheckUpdate() {
    dialog.showMessageBox(this.mainWindow!, {
      title: '检查更新',
      message: '检查更新',
      detail: '当前已是最新版本',
      buttons: ['确定'],
    })
  }
}

// 启动应用
new NovelCreationManager()
