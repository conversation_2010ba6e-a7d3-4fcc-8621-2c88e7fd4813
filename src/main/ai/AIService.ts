import { OpenAIProvider } from '../ai-providers/OpenAIProvider'
import { ZhipuProvider } from '../ai-providers/ZhipuProvider'
import { QwenProvider } from '../ai-providers/QwenProvider'
import { DoubaoProvider } from '../ai-providers/DoubaoProvider'
import { AIRequest, AIResponse } from '../../shared/types'

export interface AIProvider {
  generate(request: AIRequest): Promise<AIResponse>
  isAvailable(): boolean
  getName(): string
}

export class AIService {
  private providers: Map<string, AIProvider> = new Map()
  private defaultProvider: string = 'openai'

  constructor() {
    this.initializeProviders()
  }

  private initializeProviders() {
    // 初始化各个AI提供商
    this.providers.set('openai', new OpenAIProvider())
    this.providers.set('zhipu', new ZhipuProvider())
    this.providers.set('qwen', new QwenProvider())
    this.providers.set('doubao', new DoubaoProvider())
  }

  public async generate(request: AIRequest, provider?: string): Promise<AIResponse> {
    const targetProvider = provider || this.defaultProvider
    const aiProvider = this.providers.get(targetProvider)

    if (!aiProvider) {
      throw new Error(`AI provider '${targetProvider}' not found`)
    }

    if (!aiProvider.isAvailable()) {
      throw new Error(`AI provider '${targetProvider}' is not available`)
    }

    try {
      return await aiProvider.generate(request)
    } catch (error) {
      // 如果当前提供商不可用，尝试其他提供商
      if (provider) {
        for (const [name, fallbackProvider] of this.providers) {
          if (name !== provider && fallbackProvider.isAvailable()) {
            try {
              return await fallbackProvider.generate(request)
            } catch {
              continue
            }
          }
        }
      }
      throw error
    }
  }

  public async generateWithRetry(
    request: AIRequest,
    provider?: string,
    maxRetries: number = 3
  ): Promise<AIResponse> {
    let lastError: Error | null = null

    for (let i = 0; i < maxRetries; i++) {
      try {
        return await this.generate(request, provider)
      } catch (error) {
        lastError = error as Error
        // 如果是最后一个重试，不再等待
        if (i === maxRetries - 1) break

        // 指数退避
        const delay = Math.pow(2, i) * 1000
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }

    throw lastError || new Error('Unknown error occurred')
  }

  public async generateMultiple(
    request: AIRequest,
    providers: string[] = []
  ): Promise<AIResponse[]> {
    const targetProviders =
      providers.length > 0 ? providers : Array.from(this.providers.keys())
    const availableProviders = targetProviders.filter(p =>
      this.providers.get(p)?.isAvailable()
    )

    if (availableProviders.length === 0) {
      throw new Error('No AI providers available')
    }

    const promises = availableProviders.map(async provider => {
      try {
        return await this.generate(request, provider)
      } catch {
        return null
      }
    })

    const results = await Promise.all(promises)
    return results.filter((result): result is AIResponse => result !== null)
  }

  // 智能续写
  public async continueWriting(
    context: string,
    maxLength: number = 500,
    style?: string
  ): Promise<string> {
    const prompt = this.buildContinueWritingPrompt(context, maxLength, style)

    const request: AIRequest = {
      prompt,
      context,
      maxTokens: maxLength,
      temperature: 0.7,
    }

    const response = await this.generateWithRetry(request)
    return response.content.trim()
  }

  // 文本润色
  public async polishText(
    text: string,
    style: string = 'formal',
    focus: string[] = ['grammar', 'style', 'clarity']
  ): Promise<string> {
    const prompt = this.buildPolishPrompt(text, style, focus)

    const request: AIRequest = {
      prompt,
      context: text,
      maxTokens: Math.floor(text.length * 1.5),
      temperature: 0.3,
    }

    const response = await this.generateWithRetry(request)
    return response.content.trim()
  }

  // 剧情推荐
  public async plotRecommendation(
    currentPlot: string,
    genre: string,
    characters: string[] = []
  ): Promise<string[]> {
    const prompt = this.buildPlotPrompt(currentPlot, genre, characters)

    const request: AIRequest = {
      prompt,
      context: currentPlot,
      maxTokens: 800,
      temperature: 0.8,
    }

    const response = await this.generateWithRetry(request)
    return response.content
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0)
  }

  // 角色关系分析
  public async analyzeCharacterRelationships(
    characters: Array<{ name: string; description: string }>,
    currentStory: string
  ): Promise<
    Array<{
      character1: string
      character2: string
      relationship: string
      description: string
    }>
  > {
    const prompt = this.buildCharacterAnalysisPrompt(characters, currentStory)

    const request: AIRequest = {
      prompt,
      context: currentStory,
      maxTokens: 1000,
      temperature: 0.6,
    }

    const response = await this.generateWithRetry(request)
    return this.parseCharacterRelationships(response.content)
  }

  // 错误纠正
  public async correctText(text: string): Promise<string> {
    const prompt = `请纠正以下文本中的错误，包括错别字、语法错误、标点符号等，保持原文的意思不变：

${text}

纠正后的文本：`

    const request: AIRequest = {
      prompt,
      context: text,
      maxTokens: Math.floor(text.length * 1.2),
      temperature: 0.2,
    }

    const response = await this.generateWithRetry(request)
    return response.content.trim()
  }

  // 获取可用的提供商列表
  public getAvailableProviders(): string[] {
    return Array.from(this.providers.entries())
      .filter(([_, provider]) => provider.isAvailable())
      .map(([name, _]) => name)
  }

  // 设置默认提供商
  public setDefaultProvider(provider: string): void {
    if (this.providers.has(provider)) {
      this.defaultProvider = provider
    }
  }

  // 获取默认提供商
  public getDefaultProvider(): string {
    return this.defaultProvider
  }

  // 辅助方法：构建续写提示词
  private buildContinueWritingPrompt(
    context: string,
    maxLength: number,
    style?: string
  ): string {
    const styleInstruction = style ? `请保持${style}的写作风格。` : ''
    return `请根据以下文本内容继续写作，要求：
1. 保持故事的连贯性和逻辑性
2. 维持人物性格的一致性
3. ${styleInstruction}
4. 续写长度控制在${maxLength}字以内
5. 保持语言流畅自然

当前文本：
${context}

续写内容：`
  }

  // 辅助方法：构建润色提示词
  private buildPolishPrompt(text: string, style: string, focus: string[]): string {
    const focusInstruction = focus.join('、')
    return `请对以下文本进行润色，要求：
1. 改进${focusInstruction}
2. 采用${style}的写作风格
3. 保持原文的核心意思不变
4. 提升文本的文学性和可读性

原文：
${text}

润色后的文本：`
  }

  // 辅助方法：构建剧情推荐提示词
  private buildPlotPrompt(
    currentPlot: string,
    genre: string,
    characters: string[]
  ): string {
    const characterList =
      characters.length > 0 ? `涉及角色：${characters.join('、')}` : ''
    return `请根据以下当前剧情，为${genre}题材的小说推荐3-5个可能的剧情发展方向：

当前剧情：
${currentPlot}
${characterList}

请按以下格式提供推荐：
1. [剧情方向1]
2. [剧情方向2]
3. [剧情方向3]
...`
  }

  // 辅助方法：构建角色分析提示词
  private buildCharacterAnalysisPrompt(
    characters: Array<{ name: string; description: string }>,
    currentStory: string
  ): string {
    const characterInfo = characters.map(c => `${c.name}: ${c.description}`).join('\n')
    return `请分析以下角色在故事中的关系：

角色信息：
${characterInfo}

当前故事：
${currentStory}

请分析角色之间的关系，包括友谊、敌对、亲情、爱情等，并描述他们之间的互动模式。`
  }

  // 辅助方法：解析角色关系
  private parseCharacterRelationships(content: string): Array<{
    character1: string
    character2: string
    relationship: string
    description: string
  }> {
    // 简单的解析逻辑，实际应用中可能需要更复杂的解析
    const lines = content.split('\n')
    const relationships: {
      character1: string
      character2: string
      relationship: string
      description: string
    }[] = []

    for (const line of lines) {
      if (line.includes('和') || line.includes('与')) {
        // 简单解析，实际应用中需要更复杂的NLP处理
        const parts = line.split(/和|与/)
        if (parts.length >= 2) {
          relationships.push({
            character1: parts[0].trim(),
            character2: parts[1].split(/：|:/)[0].trim(),
            relationship: '友谊',
            description: line,
          })
        }
      }
    }

    return relationships
  }
}
