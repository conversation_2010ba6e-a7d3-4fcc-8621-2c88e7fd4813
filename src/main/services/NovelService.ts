import { DatabaseService } from '../database/DatabaseService'
import { Novel, Chapter, Character, Material, Outline } from '../../shared/types'
import { SqlRow } from 'sql.js'

export class NovelService {
  private db: DatabaseService

  constructor(db: DatabaseService) {
    this.db = db
  }

  // 小说相关操作
  async createNovel(
    novel: Omit<Novel, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<Novel> {
    const id = Date.now().toString()
    const newNovel: Novel = {
      ...novel,
      id,
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    await this.db.insert('novels', {
      id: newNovel.id,
      title: newNovel.title,
      author: newNovel.author,
      description: newNovel.description,
      cover_image: newNovel.coverImage ?? null,
      created_at: newNovel.createdAt.toISOString(),
      updated_at: newNovel.updatedAt.toISOString(),
      word_count: newNovel.wordCount,
      chapter_count: newNovel.chapterCount,
      status: newNovel.status,
      genre: JSON.stringify(newNovel.genre),
      tags: JSON.stringify(newNovel.tags),
    })

    return newNovel
  }

  async getNovel(id: string): Promise<Novel | null> {
    const row = await this.db.findById('novels', id)
    if (!row) return null

    return this.mapRowToNovel(row)
  }

  async getAllNovels(): Promise<Novel[]> {
    const rows = await this.db.findAll('novels', '1=1 ORDER BY updated_at DESC')
    return rows.map(row => this.mapRowToNovel(row))
  }

  async updateNovel(
    id: string,
    updates: Partial<
      Omit<Novel, 'id' | 'createdAt'> | { wordCount: number; chapterCount: number }
    >
  ): Promise<void> {
    const updateData: SqlRow = {}

    if ('title' in updates && updates.title !== undefined)
      updateData.title = updates.title
    if ('author' in updates && updates.author !== undefined)
      updateData.author = updates.author
    if ('description' in updates && updates.description !== undefined)
      updateData.description = updates.description
    if ('coverImage' in updates && updates.coverImage !== undefined)
      updateData.cover_image = updates.coverImage
    if ('wordCount' in updates && updates.wordCount !== undefined)
      updateData.word_count = updates.wordCount
    if ('chapterCount' in updates && updates.chapterCount !== undefined)
      updateData.chapter_count = updates.chapterCount
    if ('status' in updates && updates.status !== undefined)
      updateData.status = updates.status
    if ('genre' in updates && updates.genre !== undefined)
      updateData.genre = JSON.stringify(updates.genre)
    if ('tags' in updates && updates.tags !== undefined)
      updateData.tags = JSON.stringify(updates.tags)

    updateData.updated_at = new Date().toISOString()

    await this.db.update('novels', id, updateData)
  }

  async deleteNovel(id: string): Promise<void> {
    await this.db.delete('novels', id)
  }

  // 章节相关操作
  async createChapter(
    chapter: Omit<Chapter, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<Chapter> {
    const id = Date.now().toString()
    const newChapter: Chapter = {
      ...chapter,
      id,
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    await this.db.insert('chapters', {
      id: newChapter.id,
      novel_id: newChapter.novelId,
      title: newChapter.title,
      content: newChapter.content,
      word_count: newChapter.wordCount,
      order_index: newChapter.orderIndex,
      status: newChapter.status,
      created_at: newChapter.createdAt.toISOString(),
      updated_at: newChapter.updatedAt.toISOString(),
    })

    // 更新小说的章节数和字数
    await this.updateNovelStats(newChapter.novelId)

    return newChapter
  }

  async getChaptersByNovelId(novelId: string): Promise<Chapter[]> {
    const rows = await this.db.findAll(
      'chapters',
      'novel_id = ? ORDER BY order_index',
      [novelId]
    )
    return rows.map(row => this.mapRowToChapter(row))
  }

  async updateChapter(
    id: string,
    updates: Partial<Omit<Chapter, 'id' | 'createdAt' | 'updatedAt'>>
  ): Promise<void> {
    const updateData: SqlRow = {}

    if (updates.title !== undefined) updateData.title = updates.title
    if (updates.content !== undefined) updateData.content = updates.content
    if (updates.wordCount !== undefined) updateData.word_count = updates.wordCount
    if (updates.orderIndex !== undefined) updateData.order_index = updates.orderIndex
    if (updates.status !== undefined) updateData.status = updates.status

    updateData.updated_at = new Date().toISOString()

    await this.db.update('chapters', id, updateData)

    // 获取章节所属的小说ID并更新统计信息
    const chapter = await this.db.get('SELECT novel_id FROM chapters WHERE id = ?', [
      id,
    ])
    if (chapter) {
      await this.updateNovelStats(chapter.novel_id as string)
    }
  }

  async deleteChapter(id: string): Promise<void> {
    const chapter = await this.db.get('SELECT novel_id FROM chapters WHERE id = ?', [
      id,
    ])
    await this.db.delete('chapters', id)

    if (chapter) {
      await this.updateNovelStats(chapter.novel_id as string)
    }
  }

  // 角色相关操作
  async createCharacter(
    character: Omit<Character, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<Character> {
    const id = Date.now().toString()
    const newCharacter: Character = {
      ...character,
      id,
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    await this.db.insert('characters', {
      id: newCharacter.id,
      novel_id: newCharacter.novelId,
      name: newCharacter.name,
      alias: JSON.stringify(newCharacter.alias),
      description: newCharacter.description,
      age: newCharacter.age ?? null,
      gender: newCharacter.gender,
      personality: JSON.stringify(newCharacter.personality),
      background: newCharacter.background,
      created_at: newCharacter.createdAt.toISOString(),
      updated_at: newCharacter.updatedAt.toISOString(),
    })

    return newCharacter
  }

  async getCharactersByNovelId(novelId: string): Promise<Character[]> {
    const rows = await this.db.findAll(
      'characters',
      'novel_id = ? ORDER BY created_at',
      [novelId]
    )
    return rows.map(row => this.mapRowToCharacter(row))
  }

  async updateCharacter(
    id: string,
    updates: Partial<Omit<Character, 'id' | 'createdAt' | 'updatedAt'>>
  ): Promise<void> {
    const updateData: SqlRow = {}

    if (updates.name !== undefined) updateData.name = updates.name
    if (updates.alias !== undefined) updateData.alias = JSON.stringify(updates.alias)
    if (updates.description !== undefined) updateData.description = updates.description
    if (updates.age !== undefined) updateData.age = updates.age
    if (updates.gender !== undefined) updateData.gender = updates.gender
    if (updates.personality !== undefined)
      updateData.personality = JSON.stringify(updates.personality)
    if (updates.background !== undefined) updateData.background = updates.background

    updateData.updated_at = new Date().toISOString()

    await this.db.update('characters', id, updateData)
  }

  async deleteCharacter(id: string): Promise<void> {
    await this.db.delete('characters', id)
  }

  // 素材相关操作
  async createMaterial(
    material: Omit<Material, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<Material> {
    const id = Date.now().toString()
    const newMaterial: Material = {
      ...material,
      id,
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    await this.db.insert('materials', {
      id: newMaterial.id,
      novel_id: newMaterial.novelId ?? null,
      title: newMaterial.title,
      content: newMaterial.content,
      type: newMaterial.type,
      category: newMaterial.category,
      tags: JSON.stringify(newMaterial.tags),
      created_at: newMaterial.createdAt.toISOString(),
      updated_at: newMaterial.updatedAt.toISOString(),
    })

    return newMaterial
  }

  async getMaterialsByNovelId(novelId: string): Promise<Material[]> {
    const rows = await this.db.findAll(
      'materials',
      'novel_id = ? ORDER BY created_at DESC',
      [novelId]
    )
    return rows.map(row => this.mapRowToMaterial(row))
  }

  async getAllMaterials(): Promise<Material[]> {
    const rows = await this.db.findAll('materials', '1=1 ORDER BY created_at DESC')
    return rows.map(row => this.mapRowToMaterial(row))
  }

  async updateMaterial(
    id: string,
    updates: Partial<Omit<Material, 'id' | 'createdAt' | 'updatedAt'>>
  ): Promise<void> {
    const updateData: SqlRow = {}

    if (updates.title !== undefined) updateData.title = updates.title
    if (updates.content !== undefined) updateData.content = updates.content
    if (updates.type !== undefined) updateData.type = updates.type
    if (updates.category !== undefined) updateData.category = updates.category
    if (updates.tags !== undefined) updateData.tags = JSON.stringify(updates.tags)

    updateData.updated_at = new Date().toISOString()

    await this.db.update('materials', id, updateData)
  }

  async deleteMaterial(id: string): Promise<void> {
    await this.db.delete('materials', id)
  }

  // 大纲相关操作
  async createOutline(
    outline: Omit<Outline, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<Outline> {
    const id = Date.now().toString()
    const newOutline: Outline = {
      ...outline,
      id,
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    await this.db.insert('outlines', {
      id: newOutline.id,
      novel_id: newOutline.novelId,
      title: newOutline.title,
      content: newOutline.content,
      order_index: newOutline.orderIndex,
      level: newOutline.level,
      parent_id: newOutline.parentId ?? null,
      created_at: newOutline.createdAt.toISOString(),
      updated_at: newOutline.updatedAt.toISOString(),
    })

    return newOutline
  }

  async getOutlinesByNovelId(novelId: string): Promise<Outline[]> {
    const rows = await this.db.findAll(
      'outlines',
      'novel_id = ? ORDER BY order_index',
      [novelId]
    )
    return rows.map(row => this.mapRowToOutline(row))
  }

  async updateOutline(
    id: string,
    updates: Partial<Omit<Outline, 'id' | 'createdAt' | 'updatedAt'>>
  ): Promise<void> {
    const updateData: SqlRow = {}

    if (updates.title !== undefined) updateData.title = updates.title
    if (updates.content !== undefined) updateData.content = updates.content
    if (updates.orderIndex !== undefined) updateData.order_index = updates.orderIndex
    if (updates.level !== undefined) updateData.level = updates.level
    if (updates.parentId !== undefined) updateData.parent_id = updates.parentId

    updateData.updated_at = new Date().toISOString()

    await this.db.update('outlines', id, updateData)
  }

  async deleteOutline(id: string): Promise<void> {
    await this.db.delete('outlines', id)
  }

  // 辅助方法
  private async updateNovelStats(novelId: string): Promise<void> {
    // 计算总字数
    const wordCountResult = await this.db.get(
      'SELECT SUM(word_count) as total FROM chapters WHERE novel_id = ?',
      [novelId]
    )
    const totalWordCount = wordCountResult ? (wordCountResult.total as number) : 0

    // 计算章节数
    const chapterCount = await this.db.count('chapters', 'novel_id = ?', [novelId])

    await this.updateNovel(novelId, {
      wordCount: totalWordCount,
      chapterCount,
      updatedAt: new Date(),
    })
  }

  private mapRowToNovel(row: SqlRow): Novel {
    return {
      id: row.id as string,
      title: row.title as string,
      author: row.author as string,
      description: row.description as string,
      coverImage: row.cover_image as string,
      createdAt: new Date(row.created_at as string),
      updatedAt: new Date(row.updated_at as string),
      wordCount: row.word_count as number,
      chapterCount: row.chapter_count as number,
      status: row.status as 'draft' | 'publishing' | 'published',
      genre: JSON.parse((row.genre as string) || '[]'),
      tags: JSON.parse((row.tags as string) || '[]'),
    }
  }

  private mapRowToChapter(row: SqlRow): Chapter {
    return {
      id: row.id as string,
      novelId: row.novel_id as string,
      title: row.title as string,
      content: row.content as string,
      wordCount: row.word_count as number,
      orderIndex: row.order_index as number,
      status: row.status as 'draft' | 'completed',
      createdAt: new Date(row.created_at as string),
      updatedAt: new Date(row.updated_at as string),
    }
  }

  private mapRowToCharacter(row: SqlRow): Character {
    return {
      id: row.id as string,
      novelId: row.novel_id as string,
      name: row.name as string,
      alias: JSON.parse((row.alias as string) || '[]'),
      description: row.description as string,
      age: row.age as number,
      gender: row.gender as 'male' | 'female' | 'other',
      personality: JSON.parse((row.personality as string) || '[]'),
      background: row.background as string,
      createdAt: new Date(row.created_at as string),
      updatedAt: new Date(row.updated_at as string),
    }
  }

  private mapRowToMaterial(row: SqlRow): Material {
    return {
      id: row.id as string,
      novelId: row.novel_id as string,
      title: row.title as string,
      content: row.content as string,
      type: row.type as 'text' | 'image' | 'reference' | 'note',
      category: row.category as string,
      tags: JSON.parse((row.tags as string) || '[]'),
      createdAt: new Date(row.created_at as string),
      updatedAt: new Date(row.updated_at as string),
    }
  }

  private mapRowToOutline(row: SqlRow): Outline {
    return {
      id: row.id as string,
      novelId: row.novel_id as string,
      title: row.title as string,
      content: row.content as string,
      orderIndex: row.order_index as number,
      level: row.level as number,
      parentId: row.parent_id as string,
      createdAt: new Date(row.created_at as string),
      updatedAt: new Date(row.updated_at as string),
    }
  }
}
