import path from 'path'
import fs from 'fs'
import { app } from 'electron'
import initSqlJs, { Database, SqlRow } from 'sql.js'

export class DatabaseService {
  private db: Database | null = null
  private dbPath: string

  constructor() {
    // 在用户数据目录中创建数据库文件
    const userDataPath = app.getPath('userData')
    this.dbPath = path.join(userDataPath, 'novel_manager.db')

    this.initializeDatabase()
  }

  private async initializeDatabase() {
    try {
      // 确保数据目录存在
      const dbDir = path.dirname(this.dbPath)
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true })
      }

      // 加载或创建数据库
      const SQL = await initSqlJs()

      if (fs.existsSync(this.dbPath)) {
        const fileBuffer = fs.readFileSync(this.dbPath)
        this.db = new SQL.Database(fileBuffer)
      } else {
        this.db = new SQL.Database()
        await this.createTables()
        this.saveDatabase()
      }
    } catch (error) {
      throw error
    }
  }

  private saveDatabase(): void {
    if (!this.db) return

    const data = this.db.export()
    fs.writeFileSync(this.dbPath, Buffer.from(data))
  }

  private createTables(): void {
    if (!this.db) return

    const tables = [
      `CREATE TABLE IF NOT EXISTS novels (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        author TEXT NOT NULL,
        description TEXT,
        cover_image TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        word_count INTEGER DEFAULT 0,
        chapter_count INTEGER DEFAULT 0,
        status TEXT DEFAULT 'draft',
        genre TEXT DEFAULT '[]',
        tags TEXT DEFAULT '[]'
      )`,

      `CREATE TABLE IF NOT EXISTS chapters (
        id TEXT PRIMARY KEY,
        novel_id TEXT NOT NULL,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        word_count INTEGER DEFAULT 0,
        order_index INTEGER DEFAULT 0,
        status TEXT DEFAULT 'draft',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      `CREATE TABLE IF NOT EXISTS characters (
        id TEXT PRIMARY KEY,
        novel_id TEXT NOT NULL,
        name TEXT NOT NULL,
        alias TEXT DEFAULT '[]',
        description TEXT,
        age INTEGER,
        gender TEXT,
        personality TEXT DEFAULT '[]',
        background TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      `CREATE TABLE IF NOT EXISTS character_relationships (
        id TEXT PRIMARY KEY,
        character_id TEXT NOT NULL,
        target_character_id TEXT NOT NULL,
        relationship TEXT NOT NULL,
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      `CREATE TABLE IF NOT EXISTS outlines (
        id TEXT PRIMARY KEY,
        novel_id TEXT NOT NULL,
        title TEXT NOT NULL,
        content TEXT,
        order_index INTEGER DEFAULT 0,
        level INTEGER DEFAULT 1,
        parent_id TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      `CREATE TABLE IF NOT EXISTS materials (
        id TEXT PRIMARY KEY,
        novel_id TEXT,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        type TEXT DEFAULT 'text',
        category TEXT DEFAULT 'general',
        tags TEXT DEFAULT '[]',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      `CREATE TABLE IF NOT EXISTS ai_services (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        provider TEXT NOT NULL,
        model TEXT NOT NULL,
        api_key TEXT,
        base_url TEXT,
        enabled BOOLEAN DEFAULT 1,
        config TEXT DEFAULT '{}',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      `CREATE TABLE IF NOT EXISTS app_settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      `CREATE TABLE IF NOT EXISTS backup_history (
        id TEXT PRIMARY KEY,
        novel_id TEXT NOT NULL,
        backup_path TEXT NOT NULL,
        backup_size INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
    ]

    // 启用外键约束
    this.db.run('PRAGMA foreign_keys = ON')

    for (const sql of tables) {
      this.db.run(sql)
    }

    // 创建索引
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_chapters_novel_id ON chapters(novel_id)',
      'CREATE INDEX IF NOT EXISTS idx_characters_novel_id ON characters(novel_id)',
      'CREATE INDEX IF NOT EXISTS idx_outlines_novel_id ON outlines(novel_id)',
      'CREATE INDEX IF NOT EXISTS idx_materials_novel_id ON materials(novel_id)',
      'CREATE INDEX IF NOT EXISTS idx_character_relationships_character_id ON character_relationships(character_id)',
      'CREATE INDEX IF NOT EXISTS idx_character_relationships_target_id ON character_relationships(target_character_id)',
      'CREATE INDEX IF NOT EXISTS idx_outlines_parent_id ON outlines(parent_id)',
      'CREATE INDEX IF NOT EXISTS idx_materials_type ON materials(type)',
      'CREATE INDEX IF NOT EXISTS idx_materials_category ON materials(category)',
    ]

    for (const sql of indexes) {
      this.db.run(sql)
    }
  }

  public query(sql: string, params: (string | number | null)[] = []): SqlRow[] {
    if (!this.db) {
      throw new Error('Database not initialized')
    }

    try {
      const stmt = this.db.prepare(sql)
      const result = stmt.all(params)
      return result as SqlRow[]
    } catch (error) {
      throw error
    }
  }

  public run(
    sql: string,
    params: (string | number | null)[] = []
  ): { changes: number } {
    if (!this.db) {
      throw new Error('Database not initialized')
    }

    try {
      this.db.run(sql, params)
      const changes = this.db.getRowsModified()
      this.saveDatabase()
      return { changes }
    } catch (error) {
      throw error
    }
  }

  public get(sql: string, params: (string | number | null)[] = []): SqlRow | undefined {
    if (!this.db) {
      throw new Error('Database not initialized')
    }

    try {
      const stmt = this.db.prepare(sql)
      return stmt.get(params) as SqlRow | undefined
    } catch (error) {
      throw error
    }
  }

  public beginTransaction(): void {
    if (!this.db) {
      throw new Error('Database not initialized')
    }
    this.db.run('BEGIN TRANSACTION')
  }

  public commitTransaction(): void {
    if (!this.db) {
      throw new Error('Database not initialized')
    }
    this.db.run('COMMIT')
    this.saveDatabase()
  }

  public rollbackTransaction(): void {
    if (!this.db) {
      throw new Error('Database not initialized')
    }
    this.db.run('ROLLBACK')
  }

  public close(): void {
    if (!this.db) {
      return
    }
    this.saveDatabase()
    this.db.close()
  }

  // 便捷方法
  public findById(table: string, id: string): SqlRow | undefined {
    return this.get(`SELECT * FROM ${table} WHERE id = ?`, [id])
  }

  public findAll(
    table: string,
    conditions: string = '',
    params: (string | number | null)[] = []
  ): SqlRow[] {
    const sql = conditions
      ? `SELECT * FROM ${table} WHERE ${conditions}`
      : `SELECT * FROM ${table}`
    return this.query(sql, params)
  }

  public insert(table: string, data: SqlRow): string {
    const keys = Object.keys(data)
    const values = Object.values(data).map(v =>
      v instanceof Uint8Array ? null : (v as string | number | null)
    )
    const placeholders = keys.map(() => '?').join(', ')

    const sql = `INSERT INTO ${table} (${keys.join(', ')}) VALUES (${placeholders})`
    this.run(sql, values)
    const res = this.get('SELECT last_insert_rowid() as id')
    return res?.id ? String(res.id) : ''
  }

  public update(table: string, id: string, data: SqlRow): void {
    const keys = Object.keys(data)
    const values = Object.values(data).map(v =>
      v instanceof Uint8Array ? null : (v as string | number | null)
    )
    const setClause = keys.map(key => `${key} = ?`).join(', ')

    const sql = `UPDATE ${table} SET ${setClause} WHERE id = ?`
    this.run(sql, [...values, id])
  }

  public delete(table: string, id: string): void {
    this.run(`DELETE FROM ${table} WHERE id = ?`, [id])
  }

  public count(
    table: string,
    conditions: string = '',
    params: (string | number | null)[] = []
  ): number {
    const sql = conditions
      ? `SELECT COUNT(*) as count FROM ${table} WHERE ${conditions}`
      : `SELECT COUNT(*) as count FROM ${table}`
    const result = this.get(sql, params)
    return result?.count ? Number(result.count) : 0
  }
}
