// Type definitions for sql.js
declare module 'sql.js' {
  export type SqlValue = string | number | null | Uint8Array

  export interface SqlRow {
    [key: string]: SqlValue
  }

  export interface QueryResult {
    columns: string[]
    values: SqlValue[][]
  }

  export interface Statement {
    run(params?: SqlValue[]): void
    get(params?: SqlValue[]): SqlRow | undefined
    getAsObject(params?: SqlValue[]): SqlRow | undefined
    all(params?: SqlValue[]): SqlRow[]
    bind(params?: SqlValue[]): boolean
    step(): boolean
    reset(): void
    free(): void
  }

  export class Database {
    constructor(data?: Uint8Array | Buffer | null)
    run(query: string, params?: SqlValue[]): void
    exec(query: string): QueryResult[]
    prepare(query: string): Statement
    export(): Uint8Array
    close(): void
    getRowsModified(): number
  }

  interface SqlJsStatic {
    Database: typeof Database
  }

  function initSqlJs(config?: {
    locateFile?: (path: string) => string
  }): Promise<SqlJsStatic>

  export default initSqlJs
  export { SqlValue, Database }
}
