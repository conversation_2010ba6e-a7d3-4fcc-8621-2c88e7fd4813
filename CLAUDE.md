# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

**Novel Creation Manager (小说创作管理器)** - An Electron-based desktop application for novel writing with AI assistance. Built with React 18, TypeScript, and Vite, featuring local SQLite storage via sql.js and integration with multiple AI providers.

## Current Status (v1.0.2)

### ✅ Implemented Features (2024-12)
- Monaco Editor integration with auto-save
- Chapter management system (CRUD operations)
- AI Panel with 6 writing assistance features
- Real-time word count (Chinese/English)
- Theme system (light/dark)
- Database layer with sql.js
- IPC communication setup
- Export functionality (Markdown/TXT/HTML)

### 🚧 Known Issues
- 25 ESLint warnings (mainly `any` types)
- Build warnings from Vite CJS deprecation
- Tests not yet implemented

## Essential Commands

### Development
```bash
npm run dev           # Start Vite dev server (http://localhost:3000) + Electron app
npm run dev:vite      # Start only Vite dev server
npm run dev:electron  # Start only Electron (requires Vite server running)
```

### Building & Production
```bash
npm run build         # Build everything (main + renderer + electron)
npm run build:main    # Build Electron main process only
npm run build:renderer # Build React frontend only
npm run build:electron # Package into distributable app (.dmg/.exe/.AppImage)
```

### Testing
```bash
npm test              # Run all tests with Vitest
npm run test:unit     # Unit tests only
npm run test:integration # Integration tests only
npm run test:e2e      # Playwright E2E tests
npm run test:e2e:headed # E2E tests with visible browser
npm run test:coverage # Generate coverage report
npm run test:watch    # Watch mode for TDD
```

### Code Quality
```bash
npm run lint          # ESLint check
npm run lint:fix      # Auto-fix ESLint issues
npm run format        # Prettier format
npm run type-check    # TypeScript type checking
```

## Architecture

### Three-Process Architecture
1. **Main Process** (`src/main/`) - Electron main process handling:
   - Window management via `BrowserWindow`
   - IPC communication setup
   - Database operations through `DatabaseService`
   - AI service orchestration
   - File system operations

2. **Renderer Process** (`src/renderer/`) - React application:
   - UI components with styled-components
   - State management via Zustand stores
   - Route handling with React Router
   - Monaco Editor integration

3. **Preload Scripts** (`src/preload/`) - Secure bridge between main and renderer

### Key Architectural Patterns

#### IPC Communication Flow
```
Renderer → window.api.method() → Preload → ipcRenderer.invoke() → Main Process
Main Process → ipcMain.handle() → Service Layer → Database/AI/Storage
```

All IPC handlers are registered in `src/main/index.ts` with pattern:
- `novel:*` - Novel CRUD operations ✅
- `chapter:*` - Chapter management ✅
- `character:*` - Character management ✅
- `material:*` - Material library ✅
- `outline:*` - Outline structure ✅
- `settings:*` - App settings ✅
- `ai:*` - AI service calls ✅
- `file:*` - File operations ✅
- `database:*` - Direct DB queries ✅
- `export:*` - Export operations ✅

#### Database Layer
- **sql.js** in-memory SQLite database
- Persistence to `~/Library/Application Support/novel-creation-manager/novel_manager.db`
- Tables: novels, chapters, characters, outlines, materials, ai_services, app_settings
- All DB operations go through `DatabaseService` class
- Transaction support for data integrity

#### AI Service Architecture
```
AIService → BaseAIProvider (abstract)
         ├── OpenAIProvider
         ├── ZhipuProvider  
         ├── QwenProvider
         └── DoubaoProvider
```
- Providers configured via environment variables
- Fallback chain for reliability
- Request/response types in `shared/types`

#### State Management
Zustand stores in `src/renderer/stores/`:
- `novel.ts` - Novel data, chapters, characters
- `ui.ts` - UI state, theme, sidebar visibility

### File Organization Rules
- **Max 200 lines** per TypeScript/JavaScript file
- **Max 8 files** per directory (use subdirectories)
- Component files include styled components inline
- Services handle business logic, not components

## Critical Implementation Details

### Database Operations
- Always use parameterized queries to prevent SQL injection
- Transactions for multi-table updates
- Auto-save triggers DB persistence
- Backup system creates timestamped copies

### AI Integration
1. Check provider availability: `isAvailable()`
2. Use try-catch for all AI calls (network failures common)
3. Environment variables required:
   - `OPENAI_API_KEY`
   - `ZHIPU_API_KEY`
   - `QWEN_API_KEY`
   - `DOUBAO_API_KEY`

### Type Safety
- SQL operations use custom `SqlRow` type from `src/types/sql.d.ts`
- Shared types in `src/shared/types/index.ts`
- styled-components theme types in `src/renderer/styles/styled.d.ts`

### Export Functionality
Supports multiple formats via `StorageService`:
- Markdown: Direct text export
- TXT: Plain text with chapters
- HTML: Formatted with styles (can be opened as .doc)

### Known Issues & Solutions

#### Build Errors
- `Cannot find namespace 'SQL'` → Import `SqlRow` from 'sql.js'
- Theme type errors → Check `styled.d.ts` exists
- Missing pages → Use `pages/[feature]/[Feature]Page.tsx` pattern

#### Test Configuration
- Unit tests: `vitest.config.unit.ts` 
- Integration tests: `vitest.config.integration.ts`
- E2E tests use Playwright, separate from Vitest

## Performance Considerations

### Large Documents
- Monaco Editor handles up to 1MB efficiently
- Implement virtual scrolling for chapter lists >100 items
- Debounce auto-save (currently 2s delay)

### Database
- sql.js loads entire DB into memory
- Consider pagination for large result sets
- Batch inserts for bulk operations

### AI Responses
- Implement request cancellation
- Cache frequently used completions
- Show loading states during API calls

## Security Notes

- API keys in `.env` file (never commit)
- IPC uses contextBridge for security
- No direct node module access in renderer
- Input sanitization before database operations