# 更新日志

本文档记录了小说创作管理器的所有重要更改。格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)。

## [1.0.2] - 2024-12-07

### 新增 (Added)
- **Monaco Editor 集成**
  - 完整替换原有编辑器为 Monaco Editor（VS Code 核心）
  - 支持 Markdown 语法高亮
  - 自定义编辑器设置（字体、行高、主题等）
  - 快捷键支持（Ctrl+S 保存）
  - 状态栏显示字数和字符统计

- **智能自动保存系统**
  - 防抖处理（500ms）优化性能
  - 可配置保存间隔（默认30秒）
  - 内容变化实时检测
  - 保存状态友好显示（刚刚保存/N分钟前保存）

- **AI 写作助手增强**
  - 实现6种AI辅助功能：智能续写、文本润色、错字纠正、剧情推荐、人物分析、场景增强
  - 支持多AI提供商切换
  - 改进提示词工程，优化AI响应质量
  - 错误处理和降级机制

- **章节管理功能完善**
  - 章节侧边栏实时显示（标题、字数、状态）
  - 快速创建和删除章节
  - 章节切换保持编辑状态
  - 空状态友好提示

### 修复 (Fixed)
- **应用启动问题**
  - 修复了 Electron 应用无法启动的问题
  - 解决了 SQL.js 模块加载错误
  - 修正了构建配置，分离主进程和渲染进程的输出
  - 更正了生产环境下的文件路径问题

- **类型和样式问题**
  - 修复 TypeScript 类型错误（hover属性、any类型）
  - 解决主题切换时的样式缺失
  - 修正字数统计算法（中英文分别处理）

### 改进 (Improved)
- **构建系统**
  - 创建独立的 Electron 构建配置 (electron.vite.config.ts)
  - 优化了构建脚本，避免文件覆盖
  - 改进了 CommonJS 模块兼容性
  - 成功构建 DMG 安装包（macOS）

- **性能优化**
  - 编辑器防抖处理减少不必要的渲染
  - 优化章节切换性能
  - 减少内存占用

### 文档 (Documentation)
- 完整更新 README.md，添加功能状态标记
- 更新 CLAUDE.md 开发指南，记录当前实现状态
- 创建 FEATURES.md 详细功能清单
- 创建 CHANGELOG.md 版本更新日志

## [1.0.1] - 2024-08-06

### 新增 (Added)
- **测试基础设施**
  - 完整的单元测试套件（Vitest + React Testing Library）
  - 集成测试框架配置
  - E2E测试实现（Playwright）
  - 40+ 测试用例覆盖核心功能
  
- **CI/CD 工作流**
  - GitHub Actions CI/CD 管道
  - 自动化测试运行
  - 代码质量检查集成
  - 夜间测试和性能回归测试
  - 自动化发布流程

- **代码质量工具**
  - Husky + lint-staged 自动化检查
  - 提交前代码质量验证
  - 测试覆盖率报告
  - 性能测试集成（Lighthouse CI）

- **测试文档**
  - 完整的测试策略文档
  - 测试运行指南
  - CI/CD 配置说明

### 改进 (Improved)
- 代码质量和稳定性显著提升
- 开发流程标准化
- 自动化程度提高

## [1.0.0] - 2024-08-06

### 新增 (Added)
- **核心写作功能**
  - 智能文本编辑器，支持富文本格式
  - 实时自动保存功能，防止数据丢失
  - 章节管理系统，支持章节排序和导航
  - 字数统计和写作进度跟踪

- **AI助手功能**
  - 智能文本续写，基于上下文生成内容
  - 文本润色功能，改善语法和表达
  - 剧情推荐系统，提供情节发展建议
  - 角色关系分析，可视化角色网络
  - 多AI提供商支持（OpenAI、智谱AI、通义千问、豆包）

- **数据管理**
  - 本地SQLite数据库，无需外部数据库
  - 小说项目管理，支持多小说同时管理
  - 角色管理系统，包含详细角色信息
  - 素材库功能，支持多种素材类型
  - 大纲构建工具，支持层级结构

- **导出功能**
  - 多格式导出支持（Markdown、TXT、HTML）
  - 批量导出功能
  - 自定义导出选项
  - 元数据包含功能

- **用户界面**
  - 现代化响应式界面设计
  - 深色/浅色主题支持
  - 可自定义的工作区布局
  - 专注模式，减少干扰
  - 直观的侧边栏导航

### 技术特性 (Technical Features)
- **跨平台支持**: Windows、macOS、Linux 全平台支持
- **现代技术栈**: React 18 + TypeScript + Electron 28
- **性能优化**: 基于 Vite 的快速构建和热重载
- **类型安全**: 完整的 TypeScript 类型系统
- **模块化架构**: 清晰的代码结构和模块分离
- **数据安全**: 本地数据存储，隐私保护

### 测试与质量保证 (Testing & Quality Assurance)
- **完整测试套件**: 单元测试、集成测试、E2E测试全覆盖
- **测试框架**: Vitest + React Testing Library + Playwright
- **代码质量工具**: ESLint + Prettier + TypeScript 严格检查
- **自动化测试**: Git hooks 自动运行测试和代码检查
- **CI/CD 集成**: GitHub Actions 自动化构建和测试
- **测试覆盖率**: 代码覆盖率 ≥ 80%
- **性能测试**: Lighthouse CI 性能监控
- **安全扫描**: 自动化依赖安全检查

### 系统要求 (System Requirements)
- **操作系统**: Windows 10+, macOS 10.14+, Linux
- **Node.js**: 18.0+ (开发环境)
- **内存**: 最少 4GB RAM，推荐 8GB+
- **存储**: 最少 2GB 可用空间

## [0.9.0] - 2024-07-15

### 新增 (Added)
- 初始版本发布
- 基础编辑器功能
- 简单的文本保存功能
- 基本的用户界面

### 变更 (Changed)
- 项目架构设计
- 技术选型确定
- 开发环境搭建

## [计划中] (Planned)

### [1.1.0] - 计划发布
- **云同步功能**: 支持多设备数据同步
- **协作功能**: 多人协作编辑
- **版本控制**: 完整的文档版本历史
- **更多AI功能**: AI驱动的写作建议和优化
- **移动端支持**: iOS和Android应用
- **插件系统**: 支持第三方插件扩展

### [1.2.0] - 远期计划
- **社区功能**: 用户社区和作品分享
- **出版工具**: 直接发布到电子书平台
- **高级AI功能**: 更智能的写作助手
- **数据分析**: 写作习惯和进度分析
- **多语言支持**: 界面多语言化

---

## 版本说明

### 版本号格式
项目使用 [语义化版本](https://semver.org/) 格式：
- **主版本号**: 不兼容的API更改
- **次版本号**: 向下兼容的功能新增
- **修订号**: 向下兼容的问题修正

### 更新类型
- **新增 (Added)**: 新功能
- **变更 (Changed)**: 已有功能的变更
- **废弃 (Deprecated)**: 即将移除的功能
- **移除 (Removed)**: 已移除的功能
- **修复 (Fixed)**: 问题修正
- **安全 (Security)**: 安全相关的修复

### 更新频率
- **主版本**: 每6-12个月发布一次
- **次版本**: 每2-3个月发布一次
- **修订版本**: 根据需要随时发布

---

## 贡献

欢迎通过 [GitHub Issues](https://github.com/your-username/novel-creation-manager/issues) 报告问题或建议新功能。

## 支持

如果您需要帮助，请：
1. 查看 [用户手册](docs/user-manual.md)
2. 浏览 [常见问题](docs/faq.md)
3. 在 [GitHub Discussions](https://github.com/your-username/novel-creation-manager/discussions) 中寻求帮助
4. 联系技术支持: <EMAIL>