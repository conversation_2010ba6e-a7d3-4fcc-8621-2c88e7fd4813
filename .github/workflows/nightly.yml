name: Nightly Tests

on:
  schedule:
    # Run at 2 AM UTC every day
    - cron: '0 2 * * *'
  workflow_dispatch:

jobs:
  comprehensive-tests:
    name: Comprehensive Test Suite
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        node-version: [18, 20]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run all tests
        run: npm run test:all

      - name: Generate test report
        run: npm run test:report

      - name: Upload test results
        uses: actions/upload-artifact@v3
        with:
          name: test-results-${{ matrix.os }}-node${{ matrix.node-version }}
          path: |
            coverage/
            test-results/
            playwright-report/
          retention-days: 30

  performance-regression:
    name: Performance Regression Tests
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build:vite

      - name: Run performance tests
        run: npm run test:performance

      - name: Compare with baseline
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            const results = JSON.parse(fs.readFileSync('performance-results.json', 'utf8'));
            const baseline = JSON.parse(fs.readFileSync('performance-baseline.json', 'utf8'));
            
            let hasRegression = false;
            for (const metric in results) {
              if (results[metric] > baseline[metric] * 1.1) {
                console.error(`Performance regression detected in ${metric}: ${results[metric]} (baseline: ${baseline[metric]})`);
                hasRegression = true;
              }
            }
            
            if (hasRegression) {
              core.setFailed('Performance regression detected');
            }

  memory-leak-detection:
    name: Memory Leak Detection
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run memory leak tests
        run: npm run test:memory

      - name: Analyze heap snapshots
        run: npm run analyze:heap

      - name: Upload memory reports
        if: failure()
        uses: actions/upload-artifact@v3
        with:
          name: memory-leak-reports
          path: memory-reports/
          retention-days: 7

  accessibility-tests:
    name: Accessibility Tests
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build:vite

      - name: Run accessibility tests
        run: npm run test:a11y

      - name: Upload accessibility report
        uses: actions/upload-artifact@v3
        with:
          name: accessibility-report
          path: a11y-report/
          retention-days: 30

  dependency-check:
    name: Dependency Security Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Check for outdated dependencies
        run: npm outdated || true

      - name: Audit dependencies
        run: npm audit

      - name: Check licenses
        run: npx license-checker --production --summary

      - name: Update dependencies (PR)
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: 'chore: update dependencies'
          title: '[Automated] Update Dependencies'
          body: |
            This PR updates project dependencies to their latest versions.
            
            Please review the changes and ensure all tests pass before merging.
          branch: automated/update-dependencies
          delete-branch: true

  report-status:
    name: Report Test Status
    needs: [comprehensive-tests, performance-regression, memory-leak-detection, accessibility-tests, dependency-check]
    if: always()
    runs-on: ubuntu-latest
    steps:
      - name: Generate summary report
        uses: actions/github-script@v6
        with:
          script: |
            const jobs = ${{ toJSON(needs) }};
            let report = '# Nightly Test Results\n\n';
            
            for (const [jobName, jobData] of Object.entries(jobs)) {
              const status = jobData.result;
              const emoji = status === 'success' ? '✅' : status === 'failure' ? '❌' : '⚠️';
              report += `${emoji} **${jobName}**: ${status}\n`;
            }
            
            core.summary.addRaw(report).write();

      - name: Send notification
        if: ${{ secrets.SLACK_WEBHOOK_URL != '' }}
        uses: 8398a7/action-slack@v3
        with:
          status: custom
          custom_payload: |
            {
              "text": "Nightly Test Results",
              "attachments": [{
                "color": "${{ contains(needs.*.result, 'failure') && 'danger' || 'good' }}",
                "fields": [
                  {
                    "title": "Comprehensive Tests",
                    "value": "${{ needs.comprehensive-tests.result }}",
                    "short": true
                  },
                  {
                    "title": "Performance",
                    "value": "${{ needs.performance-regression.result }}",
                    "short": true
                  },
                  {
                    "title": "Memory Leaks",
                    "value": "${{ needs.memory-leak-detection.result }}",
                    "short": true
                  },
                  {
                    "title": "Accessibility",
                    "value": "${{ needs.accessibility-tests.result }}",
                    "short": true
                  }
                ]
              }]
            }
          webhook_url: ${{ secrets.SLACK_WEBHOOK_URL }}