name: Release

on:
  push:
    tags:
      - 'v*'

jobs:
  create-release:
    name: Create Release
    runs-on: ubuntu-latest
    outputs:
      upload_url: ${{ steps.create_release.outputs.upload_url }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Create Release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref }}
          release_name: Release ${{ github.ref }}
          draft: false
          prerelease: false
          body: |
            ## 📦 小说创作管理器 ${{ github.ref }}
            
            ### ✨ 新功能
            - 详见 [CHANGELOG.md](https://github.com/${{ github.repository }}/blob/main/CHANGELOG.md)
            
            ### 🐛 修复
            - 详见 [CHANGELOG.md](https://github.com/${{ github.repository }}/blob/main/CHANGELOG.md)
            
            ### 📥 下载
            请根据您的操作系统选择对应的安装包：
            - **Windows**: `小说创作管理器-Setup-*.exe`
            - **macOS**: `小说创作管理器-*.dmg`
            - **Linux**: `小说创作管理器-*.AppImage`

  build-and-upload:
    name: Build and Upload Release Assets
    needs: create-release
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        include:
          - os: ubuntu-latest
            artifact_name: '*.AppImage'
            asset_name: novel-creation-manager-linux
          - os: windows-latest
            artifact_name: '*.exe'
            asset_name: novel-creation-manager-windows
          - os: macos-latest
            artifact_name: '*.dmg'
            asset_name: novel-creation-manager-macos

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: |
          npm run build:vite
          npm run build:electron
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Upload Release Asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.upload_url }}
          asset_path: ./release/${{ matrix.artifact_name }}
          asset_name: ${{ matrix.asset_name }}-${{ github.ref_name }}
          asset_content_type: application/octet-stream

  publish-npm:
    name: Publish to npm (if applicable)
    needs: create-release
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          registry-url: 'https://registry.npmjs.org'

      - name: Install dependencies
        run: npm ci

      - name: Build package
        run: npm run build:vite

      - name: Publish to npm
        if: ${{ secrets.NPM_TOKEN != '' }}
        run: npm publish --access public
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

  update-homebrew:
    name: Update Homebrew Formula (macOS)
    needs: build-and-upload
    runs-on: ubuntu-latest
    if: ${{ secrets.HOMEBREW_TAP_TOKEN != '' }}
    steps:
      - name: Update Homebrew formula
        uses: mislav/bump-homebrew-formula-action@v2
        with:
          formula-name: novel-creation-manager
          homebrew-tap: ${{ github.repository_owner }}/homebrew-tap
          download-url: https://github.com/${{ github.repository }}/releases/download/${{ github.ref_name }}/novel-creation-manager-macos-${{ github.ref_name }}.dmg
        env:
          COMMITTER_TOKEN: ${{ secrets.HOMEBREW_TAP_TOKEN }}