# 小说创作管理器 (Novel Creation Manager)

![License: MIT](https://img.shields.io/badge/license-MIT-blue.svg)
![Version: 1.0.2](https://img.shields.io/badge/version-1.0.2-brightgreen.svg)
![Platform: Windows, macOS, Linux](https://img.shields.io/badge/platform-Windows%20%7C%20macOS%20%7C%20Linux-lightgrey.svg)
![Framework: Electron](https://img.shields.io/badge/framework-Electron-47848F.svg)
![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)

一款专为小说创作者设计的智能写作助手，集创意构思、文章撰写、数据安全备份以及多格式导出于一体。采用现代化技术栈，支持跨平台运行，为创作者提供完整的写作解决方案。

## ✨ 核心功能

### 📝 智能写作编辑器 ✅
- **Monaco Editor 集成**: 基于微软 Monaco Editor 的专业级编辑体验
- **实时自动保存**: 智能防抖处理，可配置保存间隔（默认30秒）
- **智能字数统计**: 中文按字符、英文按单词的精确统计
- **Markdown 支持**: 完整的 Markdown 语法高亮和预览
- **主题切换**: 支持明暗主题自动切换
- **快捷键支持**: Ctrl+S 保存等常用快捷键

### 🤖 AI 写作助手 ✅
- **智能续写**: 基于上下文的智能文本续写（200-300字）
- **文本润色**: 改善文笔、优化表达方式
- **错字纠正**: 自动检测并修正错别字和语法错误
- **剧情推荐**: 提供3个可能的剧情发展方向
- **人物分析**: 分析文本中的人物特征和性格
- **增强描写**: 为场景添加更多细节和生动描写
- **多模型支持**: 集成 OpenAI、智谱AI、通义千问、豆包

### 📊 故事结构管理 ✅
- **章节管理**: 完整的章节创建、编辑、删除功能
- **章节排序**: 拖拽排序，灵活调整章节顺序
- **大纲构建**: 层级化的故事大纲管理
- **角色管理**: 详细的角色信息库（姓名、性格、背景等）
- **素材库**: 分类存储研究资料、灵感和笔记
- **实时统计**: 章节字数、状态、进度实时显示

### 💾 数据安全与备份 ✅
- **本地存储**: 基于 sql.js 的纯 JavaScript SQLite 实现
- **自动保存**: 智能防抖，避免频繁写入
- **数据持久化**: 自动保存到本地文件系统
- **隐私保护**: 所有数据本地存储，完全离线可用
- **事务支持**: 数据库事务确保数据一致性

### 📤 多格式导出 ✅
- **格式支持**: Markdown、TXT、HTML (可作为DOC打开)
- **批量导出**: 支持整部小说或选择性导出
- **元数据包含**: 自动包含作者信息和作品元数据
- **样式定制**: HTML导出包含样式，可直接打印

## 📊 实现状态

### ✅ 已完成功能（100%）
- Monaco Editor 编辑器集成
- 章节管理系统（创建、编辑、删除、排序）
- 自动保存与手动保存
- 智能字数统计
- AI 写作助手（6种AI功能）
- 数据库层（sql.js + SQLite）
- IPC 通信机制
- 主题系统（明/暗）
- 多格式导出

### 🚧 开发中功能（70%）
- 角色关系图谱可视化
- 大纲拖拽编辑
- 版本历史
- 自动备份系统

### 📋 计划功能
- 云同步
- 协作编辑
- 插件系统
- 移动端支持

## 🚀 快速开始

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```

### 构建应用
```bash
# 构建主进程和渲染进程
npm run build

# 或分别构建
npm run build:main     # 构建 Electron 主进程
npm run build:renderer # 构建前端界面
npm run build:electron # 打包成可执行文件
```

### 运行构建后的应用
```bash
npm run dev:electron
```

## 🛠 技术栈

### 前端技术
- **React 18.3**: 最新版 React，支持并发特性和 Suspense
- **TypeScript 5.3**: 类型安全，更好的开发体验
- **Vite 5.4**: 极速的前端构建工具
- **Monaco Editor**: 微软开源的代码编辑器（VS Code 同款）
- **Styled Components 6.1**: CSS-in-JS 样式解决方案
- **Zustand 4.5**: 轻量级状态管理（仅 8KB）
- **React Router 6.22**: 客户端路由管理

### 桌面应用
- **Electron 28.3**: 最新版跨平台桌面应用框架
- **sql.js 1.10**: 纯 JavaScript SQLite 实现
- **Electron Builder 24.13**: 自动化打包和分发
- **UUID 9.0**: 唯一标识符生成

### 开发工具
- **ESLint 8.57**: 代码质量检查
- **Prettier 3.2**: 代码格式化
- **TypeScript 5.3**: 静态类型检查
- **@types/react**: React 类型定义

### 测试框架
- **Vitest**: 单元测试和集成测试框架
- **React Testing Library**: React 组件测试
- **Playwright**: 端到端（E2E）测试
- **Lighthouse CI**: 性能测试
- **Coverage**: 代码覆盖率分析

## 🚀 快速开始

### 环境要求
- **Node.js**: 18.0 或更高版本
- **npm**: 8.0 或更高版本
- **操作系统**: Windows 10+, macOS 10.14+, Linux

### 安装和运行

1. **克隆项目**
   ```bash
   git clone https://github.com/your-username/novel-creation-manager.git
   cd novel-creation-manager
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **启动开发环境**
   ```bash
   npm run dev
   ```

4. **构建生产版本**
   ```bash
   npm run build
   ```

### 可用命令

```bash
# 开发模式
npm run dev                  # 启动开发服务器
npm run preview             # 预览生产构建

# 构建相关
npm run build               # 构建生产版本
npm run build:vite          # 仅构建前端
npm run build:electron      # 仅打包 Electron 应用

# 代码质量
npm run lint                # 代码检查
npm run lint:fix            # 自动修复代码问题
npm run format              # 格式化代码
npm run format:check        # 检查代码格式
npm run type-check          # TypeScript 类型检查

# 测试命令
npm test                    # 运行默认测试
npm run test:unit           # 运行单元测试
npm run test:integration    # 运行集成测试
npm run test:e2e            # 运行端到端测试
npm run test:e2e:headed     # 运行端到端测试（带界面）
npm run test:coverage       # 生成测试覆盖率报告
npm run test:watch          # 监视模式运行测试
npm run test:all            # 运行所有测试套件

# 分析工具
npm run analyze:bundle      # 分析包大小
npm run analyze:deps        # 检查依赖
npm run audit:fix           # 修复安全漏洞

# 清理命令
npm run clean               # 清理构建文件
npm run clean:cache         # 清理缓存
npm run clean:all           # 清理所有生成文件
```

## 📁 项目结构

```
novel-creation-manager/
├── src/
│   ├── main/                    # Electron 主进程
│   │   ├── index.ts            # 主进程入口
│   │   ├── database/           # 数据库服务
│   │   │   └── DatabaseService.ts
│   │   ├── ai/                 # AI 服务集成
│   │   │   ├── AIService.ts
│   │   │   └── providers/       # AI 提供商实现
│   │   ├── services/           # 业务服务
│   │   │   ├── NovelService.ts
│   │   │   ├── SettingsService.ts
│   │   │   └── StorageService.ts
│   │   └── storage/            # 存储管理
│   │       └── StorageService.ts
│   ├── renderer/               # 渲染进程 (前端)
│   │   ├── components/         # React 组件
│   │   │   ├── common/         # 通用组件
│   │   │   ├── editor/         # 编辑器组件
│   │   │   ├── ai/            # AI 功能组件
│   │   │   └── materials/     # 素材库组件
│   │   ├── pages/              # 页面组件
│   │   │   ├── Dashboard.tsx
│   │   │   ├── EditorPage.tsx
│   │   │   ├── MaterialsPage.tsx
│   │   │   └── OutlinePage.tsx
│   │   ├── stores/             # 状态管理
│   │   │   ├── novelStore.ts
│   │   │   ├── editorStore.ts
│   │   │   └── settingsStore.ts
│   │   ├── services/           # 前端服务
│   │   ├── utils/              # 工具函数
│   │   ├── types/              # TypeScript 类型定义
│   │   └── App.tsx             # 应用入口
│   ├── shared/                 # 共享代码
│   │   ├── types/              # 共享类型定义
│   │   └── utils/              # 共享工具函数
│   └── preload/                # 预加载脚本
├── tests/                      # 测试文件
│   ├── unit/                   # 单元测试
│   ├── integration/            # 集成测试
│   └── e2e/                    # 端到端测试
├── dist/                       # 构建输出
├── release/                    # 发布包
├── public/                     # 静态资源
├── docs/                       # 文档
├── scripts/                    # 构建和测试脚本
├── .github/                    # GitHub Actions 配置
│   └── workflows/              # CI/CD 工作流
├── package.json               # 项目配置
├── vite.config.ts             # Vite 配置
├── tsconfig.json              # TypeScript 配置
├── playwright.config.ts       # Playwright 配置
└── README.md                  # 项目说明
```

## 🔧 配置说明

### AI 服务配置

创建 `.env` 文件并配置以下环境变量：

```bash
# OpenAI
OPENAI_API_KEY=your_openai_api_key
OPENAI_BASE_URL=https://api.openai.com/v1

# 智谱AI
ZHIPU_API_KEY=your_zhipu_api_key

# 通义千问
QWEN_API_KEY=your_qwen_api_key

# 豆包
DOUBAO_API_KEY=your_doubao_api_key
```

支持的 AI 功能：
- 智能续写（200-300字）
- 文本润色
- 错字纠正
- 剧情推荐（3个方向）
- 人物分析
- 场景描写增强

### 数据库配置
- 使用 sql.js 实现，无需额外安装数据库
- 数据存储在用户数据目录
- 支持自动备份和手动备份

## 📖 详细文档

- [用户手册](docs/user-manual.md) - 详细的使用说明
- [技术架构](docs/architecture.md) - 系统架构和设计模式
- [API 文档](docs/api.md) - API 接口文档
- [测试文档](docs/testing.md) - 测试策略和指南
- [部署指南](docs/deployment.md) - 构建和部署说明
- [贡献指南](docs/contributing.md) - 如何参与项目开发
- [常见问题](docs/faq.md) - 常见问题解答

## 🤝 贡献指南

我们欢迎任何形式的贡献！请阅读 [贡献指南](docs/contributing.md) 了解如何参与项目开发。

### 开发流程
1. Fork 本项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

### 代码规范
- 使用 TypeScript 编写代码
- 遵循 ESLint 和 Prettier 规范
- 编写必要的测试和文档
- 确保代码通过类型检查
- 保持测试覆盖率 ≥ 80%
- 所有 PR 必须通过 CI/CD 检查

## 🐛 问题反馈

如果您遇到问题或有改进建议，请：
1. 查看 [常见问题](docs/faq.md)
2. 搜索现有的 [Issues](https://github.com/your-username/novel-creation-manager/issues)
3. 创建新的 Issue 描述问题

## 📄 许可证

本项目采用 [MIT 许可证](LICENSE) - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

感谢以下开源项目和贡献者：
- [Electron](https://www.electronjs.org/) - 跨平台桌面应用框架
- [React](https://reactjs.org/) - 用户界面构建库
- [Monaco Editor](https://microsoft.github.io/monaco-editor/) - 代码编辑器
- [sql.js](https://sql.js.org/) - JavaScript SQLite 库

## 📞 联系方式

- **项目主页**: https://github.com/your-username/novel-creation-manager
- **问题反馈**: https://github.com/your-username/novel-creation-manager/issues
- **邮件联系**: <EMAIL>

---

⭐ 如果这个项目对您有帮助，请考虑给我们一个 Star！