# Dependencies
node_modules/
.pnp
.pnp.js

# Production
dist/
dist-ssr/
build/
*.local

# Testing
coverage/
*.lcov
.nyc_output
test-results/
playwright-report/
playwright/.cache/
.vitest/

# Environment Variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
.idea/
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*.swp
*~

# OS Files
Thumbs.db
desktop.ini

# Database
*.db
*.sqlite
data/
backups/

# Electron
release/
out/
.electron/

# Cache
.cache/
.temp/
.tmp/
node_modules/.cache/
node_modules/.vite/

# Debug
.vscode-test/
*.stackdump

# Package files
*.tgz
package-lock.json.*.json

# Performance
lighthouse-report/
performance-results.json

# Memory profiling
*.heapsnapshot
memory-reports/

# Documentation build
docs/.vitepress/dist/
docs/.vitepress/cache/

# Miscellaneous
.husky/_/
.lint-staged/
.eslintcache
.prettierignore
.stylelintcache