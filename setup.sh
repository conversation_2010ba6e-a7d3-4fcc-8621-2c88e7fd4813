#!/bin/bash

# 小说创作管理器开发环境配置脚本
echo "🚀 开始配置小说创作管理器开发环境..."

# 检查Homebrew
if ! command -v brew &> /dev/null; then
    echo "❌ 需要安装Homebrew，请先运行以下命令："
    echo '/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"'
    exit 1
fi

# 安装Node.js (如果版本过低)
NODE_VERSION=$(node -v | cut -d'v' -f2)
REQUIRED_VERSION="18.0.0"
if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    echo "⚠️  Node.js版本过低，正在安装最新版本..."
    brew install node
fi

# 安装pnpm (推荐包管理器)
if ! command -v pnpm &> /dev/null; then
    echo "📦 安装pnpm..."
    npm install -g pnpm
fi

# 安装Python依赖管理工具
echo "🐍 配置Python环境..."
pip3 install --user pipenv --break-system-packages

# 安装全局开发工具
echo "🛠️  安装全局开发工具..."
npm install -g typescript @types/node vite eslint prettier jest

# 创建项目虚拟环境
echo "🔧 创建Python虚拟环境..."
python3 -m venv venv
source venv/bin/activate

# 安装Python依赖
pip install pip-tools --break-system-packages

echo "✅ 开发环境配置完成！"
echo ""
echo "📋 下一步操作："
echo "1. 激活虚拟环境: source venv/bin/activate"
echo "2. 安装项目依赖: pnpm install"
echo "3. 启动开发服务器: pnpm dev"