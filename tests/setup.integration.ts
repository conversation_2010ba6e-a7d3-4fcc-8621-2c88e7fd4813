import { vi } from 'vitest'

// Mock database for integration tests
const mockDatabase = {
  prepare: vi.fn(),
  exec: vi.fn(),
  close: vi.fn(),
}

// Mock file system
const mockFs = {
  readFile: vi.fn(),
  writeFile: vi.fn(),
  existsSync: vi.fn(),
  mkdirSync: vi.fn(),
}

// Mock Electron main process APIs
global.require = ((module: string) => {
  if (module === 'electron') {
    return {
      app: {
        getPath: vi.fn(() => '/tmp'),
        getVersion: vi.fn(() => '1.0.0'),
      },
      ipcMain: {
        handle: vi.fn(),
        on: vi.fn(),
        removeHandler: vi.fn(),
      },
      BrowserWindow: vi.fn().mockImplementation(() => ({
        loadURL: vi.fn(),
        on: vi.fn(),
        show: vi.fn(),
        close: vi.fn(),
        webContents: {
          send: vi.fn(),
          on: vi.fn(),
        },
      })),
    }
  }
  if (module === 'fs') {
    return mockFs
  }
  if (module === 'better-sqlite3') {
    return function () {
      return mockDatabase
    }
  }
  return require(module)
}) as any

// Setup test environment
process.env.NODE_ENV = 'test'
