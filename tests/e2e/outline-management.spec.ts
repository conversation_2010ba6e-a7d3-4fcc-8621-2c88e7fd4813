import { test, expect } from './fixtures'

test.describe('Outline Management', () => {
  test.beforeEach(async ({ helpers, testData }) => {
    // Create a novel and navigate to outline page
    await helpers.createNovel(testData.novelTitle)
    await helpers.navigateTo('outline')
  })

  test('should create and manage story outline', async ({ page }) => {
    await test.step('Create outline structure', async () => {
      // Create main plot points
      const plotPoints = [
        { title: '开端', description: '故事的起点，介绍主角和背景' },
        { title: '发展', description: '冲突产生，情节推进' },
        { title: '高潮', description: '矛盾激化，达到顶点' },
        { title: '结局', description: '冲突解决，故事收尾' },
      ]

      for (const point of plotPoints) {
        await page.click('[data-testid="add-plot-point-btn"]')
        await page.fill('[data-testid="plot-title-input"]', point.title)
        await page.fill('[data-testid="plot-description-input"]', point.description)
        await page.click('[data-testid="save-plot-point"]')

        // Verify plot point is added
        await expect(
          page.locator(`[data-testid="plot-point-card"][data-title="${point.title}"]`)
        ).toBeVisible()
      }
    })

    await test.step('Add sub-plots', async () => {
      // Click on the first plot point to expand
      await page.click('[data-testid="plot-point-card"][data-title="开端"]')

      // Add sub-plots
      await page.click('[data-testid="add-subplot-btn"]')
      await page.fill('[data-testid="subplot-title-input"]', '人物介绍')
      await page.fill('[data-testid="subplot-description-input"]', '详细介绍主要角色')
      await page.click('[data-testid="save-subplot"]')

      // Verify subplot is added
      await expect(
        page.locator('[data-testid="subplot-item"][data-title="人物介绍"]')
      ).toBeVisible()
    })

    await test.step('Drag and drop to reorder', async () => {
      // Get plot point elements
      const secondPoint = page.locator(
        '[data-testid="plot-point-card"][data-title="发展"]'
      )
      const thirdPoint = page.locator(
        '[data-testid="plot-point-card"][data-title="高潮"]'
      )

      // Drag second point to third position
      await secondPoint.dragTo(thirdPoint)

      // Verify order changed
      const plotPoints = page.locator('[data-testid="plot-point-card"]')
      await expect(plotPoints.nth(1)).toHaveAttribute('data-title', '高潮')
      await expect(plotPoints.nth(2)).toHaveAttribute('data-title', '发展')
    })

    await test.step('Link chapters to outline', async () => {
      // Click on a plot point
      await page.click('[data-testid="plot-point-card"][data-title="开端"]')

      // Link chapter
      await page.click('[data-testid="link-chapter-btn"]')
      await page.selectOption('[data-testid="chapter-select"]', '第一章')
      await page.click('[data-testid="confirm-link"]')

      // Verify link indicator
      await expect(
        page.locator(
          '[data-testid="plot-point-card"][data-title="开端"] [data-testid="linked-chapter-badge"]'
        )
      ).toContainText('第一章')
    })

    await test.step('Export outline', async () => {
      // Click export button
      await page.click('[data-testid="export-outline-btn"]')

      // Select format
      await page.selectOption('[data-testid="export-format-select"]', 'markdown')

      // Export
      const downloadPromise = page.waitForEvent('download')
      await page.click('[data-testid="confirm-export"]')
      const download = await downloadPromise

      // Verify download
      expect(download.suggestedFilename()).toContain('outline')
      expect(download.suggestedFilename()).toContain('.md')
    })
  })

  test('should manage timeline events', async ({ page }) => {
    await test.step('Create timeline events', async () => {
      // Switch to timeline view
      await page.click('[data-testid="timeline-tab"]')

      const events = [
        { date: '第1天', event: '主角出发', location: '村庄' },
        { date: '第3天', event: '遭遇危机', location: '森林' },
        { date: '第7天', event: '找到线索', location: '古庙' },
        { date: '第10天', event: '最终决战', location: '山顶' },
      ]

      for (const event of events) {
        await page.click('[data-testid="add-timeline-event-btn"]')
        await page.fill('[data-testid="event-date-input"]', event.date)
        await page.fill('[data-testid="event-description-input"]', event.event)
        await page.fill('[data-testid="event-location-input"]', event.location)
        await page.click('[data-testid="save-timeline-event"]')
      }

      // Verify events are displayed
      for (const event of events) {
        await expect(
          page.locator(`[data-testid="timeline-event"][data-event="${event.event}"]`)
        ).toBeVisible()
      }
    })

    await test.step('View timeline visualization', async () => {
      // Switch to visual timeline
      await page.click('[data-testid="visual-timeline-btn"]')

      // Verify timeline is rendered
      await expect(page.locator('[data-testid="timeline-visualization"]')).toBeVisible()

      // Verify events are positioned correctly
      const events = page.locator('[data-testid="timeline-node"]')
      await expect(events).toHaveCount(4)
    })

    await test.step('Filter timeline by location', async () => {
      // Apply location filter
      await page.selectOption('[data-testid="location-filter"]', '森林')

      // Verify only filtered events are shown
      await expect(page.locator('[data-testid="timeline-event"]:visible')).toHaveCount(
        1
      )
      await expect(
        page.locator('[data-testid="timeline-event"]:visible')
      ).toContainText('遭遇危机')

      // Clear filter
      await page.selectOption('[data-testid="location-filter"]', '')
      await expect(page.locator('[data-testid="timeline-event"]:visible')).toHaveCount(
        4
      )
    })
  })

  test('should create and manage scene cards', async ({ page }) => {
    await test.step('Create scene cards', async () => {
      // Switch to scene cards view
      await page.click('[data-testid="scenes-tab"]')

      // Create a scene
      await page.click('[data-testid="add-scene-btn"]')

      // Fill scene details
      await page.fill('[data-testid="scene-title-input"]', '决斗场景')
      await page.fill('[data-testid="scene-location-input"]', '竞技场')
      await page.fill('[data-testid="scene-time-input"]', '黄昏')
      await page.fill('[data-testid="scene-characters-input"]', '主角, 反派')
      await page.fill(
        '[data-testid="scene-description-textarea"]',
        '在夕阳的余晖下，主角与反派在竞技场中央对峙...'
      )

      // Add mood/atmosphere
      await page.selectOption('[data-testid="scene-mood-select"]', 'tense')

      // Save scene
      await page.click('[data-testid="save-scene"]')

      // Verify scene card is created
      await expect(
        page.locator('[data-testid="scene-card"][data-title="决斗场景"]')
      ).toBeVisible()
    })

    await test.step('Edit scene card', async () => {
      // Click to edit
      await page.click(
        '[data-testid="scene-card"][data-title="决斗场景"] [data-testid="edit-scene-btn"]'
      )

      // Update description
      await page.fill(
        '[data-testid="scene-description-textarea"]',
        '更新后的场景描述，增加了更多细节...'
      )

      // Save changes
      await page.click('[data-testid="save-scene"]')

      // Verify changes are saved
      await expect(
        page.locator('[data-testid="scene-card"][data-title="决斗场景"]')
      ).toContainText('更新后的场景描述')
    })

    await test.step('Link scene to chapter', async () => {
      // Open link dialog
      await page.click(
        '[data-testid="scene-card"][data-title="决斗场景"] [data-testid="link-scene-btn"]'
      )

      // Select chapter
      await page.selectOption('[data-testid="chapter-link-select"]', '第三章')

      // Confirm link
      await page.click('[data-testid="confirm-scene-link"]')

      // Verify link badge appears
      await expect(
        page.locator(
          '[data-testid="scene-card"][data-title="决斗场景"] [data-testid="chapter-badge"]'
        )
      ).toContainText('第三章')
    })

    await test.step('Group scenes by location', async () => {
      // Create multiple scenes with same location
      const scenes = [
        { title: '初遇', location: '竞技场' },
        { title: '训练', location: '竞技场' },
        { title: '休息', location: '旅馆' },
      ]

      for (const scene of scenes) {
        await page.click('[data-testid="add-scene-btn"]')
        await page.fill('[data-testid="scene-title-input"]', scene.title)
        await page.fill('[data-testid="scene-location-input"]', scene.location)
        await page.click('[data-testid="save-scene"]')
      }

      // Group by location
      await page.click('[data-testid="group-by-location-btn"]')

      // Verify grouping
      await expect(
        page.locator('[data-testid="location-group"][data-location="竞技场"]')
      ).toBeVisible()
      await expect(
        page.locator(
          '[data-testid="location-group"][data-location="竞技场"] [data-testid="scene-card"]'
        )
      ).toHaveCount(3) // Including the first one
    })
  })

  test('should create story arc overview', async ({ page }) => {
    await test.step('Define story arcs', async () => {
      // Switch to story arc view
      await page.click('[data-testid="story-arc-tab"]')

      // Create main arc
      await page.click('[data-testid="add-arc-btn"]')
      await page.fill('[data-testid="arc-name-input"]', '主线：寻找真相')
      await page.fill(
        '[data-testid="arc-description-input"]',
        '主角追寻身世之谜的主要故事线'
      )
      await page.selectOption('[data-testid="arc-type-select"]', 'main')
      await page.click('[data-testid="save-arc"]')

      // Create sub-arc
      await page.click('[data-testid="add-arc-btn"]')
      await page.fill('[data-testid="arc-name-input"]', '支线：友情考验')
      await page.fill(
        '[data-testid="arc-description-input"]',
        '主角与伙伴之间的关系发展'
      )
      await page.selectOption('[data-testid="arc-type-select"]', 'subplot')
      await page.click('[data-testid="save-arc"]')

      // Verify arcs are created
      await expect(
        page.locator('[data-testid="arc-card"][data-name="主线：寻找真相"]')
      ).toBeVisible()
      await expect(
        page.locator('[data-testid="arc-card"][data-name="支线：友情考验"]')
      ).toBeVisible()
    })

    await test.step('Map chapters to arcs', async () => {
      // Expand main arc
      await page.click('[data-testid="arc-card"][data-name="主线：寻找真相"]')

      // Add chapter mappings
      await page.click('[data-testid="map-chapters-btn"]')

      // Select multiple chapters
      await page.check('[data-testid="chapter-checkbox"][data-chapter="第一章"]')
      await page.check('[data-testid="chapter-checkbox"][data-chapter="第二章"]')
      await page.check('[data-testid="chapter-checkbox"][data-chapter="第三章"]')

      // Save mapping
      await page.click('[data-testid="save-chapter-mapping"]')

      // Verify chapters are mapped
      await expect(
        page.locator(
          '[data-testid="arc-card"][data-name="主线：寻找真相"] [data-testid="chapter-count"]'
        )
      ).toContainText('3 章节')
    })

    await test.step('Visualize arc progression', async () => {
      // Open arc visualization
      await page.click('[data-testid="visualize-arcs-btn"]')

      // Verify visualization is displayed
      await expect(page.locator('[data-testid="arc-visualization"]')).toBeVisible()

      // Verify arc lines are rendered
      await expect(
        page.locator('[data-testid="arc-line"][data-arc="主线：寻找真相"]')
      ).toBeVisible()
      await expect(
        page.locator('[data-testid="arc-line"][data-arc="支线：友情考验"]')
      ).toBeVisible()

      // Verify intersection points
      await expect(
        page.locator('[data-testid="arc-intersection"]')
      ).toHaveCount.greaterThan(0)
    })
  })

  test('should handle outline templates', async ({ page }) => {
    await test.step('Load outline template', async () => {
      // Open template selector
      await page.click('[data-testid="load-template-btn"]')

      // Select a template
      await page.click(
        '[data-testid="template-option"][data-template="three-act-structure"]'
      )

      // Confirm loading
      await page.click('[data-testid="confirm-load-template"]')

      // Verify template structure is loaded
      await expect(page.locator('[data-testid="plot-point-card"]')).toHaveCount(3)
      await expect(
        page.locator('[data-testid="plot-point-card"][data-title="Act 1"]')
      ).toBeVisible()
    })

    await test.step('Save custom template', async () => {
      // After creating some outline structure
      await page.click('[data-testid="save-as-template-btn"]')

      // Enter template name
      await page.fill('[data-testid="template-name-input"]', '我的模板')
      await page.fill(
        '[data-testid="template-description-input"]',
        '自定义故事结构模板'
      )

      // Save template
      await page.click('[data-testid="save-template"]')

      // Verify template is saved
      await expect(page.locator('[data-testid="success-message"]')).toContainText(
        '模板已保存'
      )

      // Verify template appears in list
      await page.click('[data-testid="load-template-btn"]')
      await expect(
        page.locator('[data-testid="template-option"][data-template="我的模板"]')
      ).toBeVisible()
    })
  })
})
