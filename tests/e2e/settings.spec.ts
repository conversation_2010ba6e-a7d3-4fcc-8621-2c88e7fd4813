import { test, expect } from './fixtures'

test.describe('Settings Management', () => {
  test.beforeEach(async ({ helpers }) => {
    // Navigate to settings page
    await helpers.navigateTo('settings')
  })

  test('should change application theme', async ({ page }) => {
    await test.step('Switch to dark theme', async () => {
      // Select theme tab
      await page.click('[data-testid="theme-tab"]')

      // Select dark theme
      await page.click('[data-testid="theme-option-dark"]')

      // Verify theme is applied
      await expect(page.locator('body')).toHaveClass(/dark-theme/)

      // Verify setting is saved
      await expect(page.locator('[data-testid="theme-status"]')).toContainText(
        '主题已更新'
      )
    })

    await test.step('Switch to light theme', async () => {
      await page.click('[data-testid="theme-option-light"]')

      // Verify theme is applied
      await expect(page.locator('body')).toHaveClass(/light-theme/)
    })

    await test.step('Test custom theme colors', async () => {
      // Enable custom theme
      await page.click('[data-testid="custom-theme-toggle"]')

      // Set primary color
      await page.fill('[data-testid="primary-color-input"]', '#FF5722')

      // Set secondary color
      await page.fill('[data-testid="secondary-color-input"]', '#4CAF50')

      // Apply custom theme
      await page.click('[data-testid="apply-custom-theme"]')

      // Verify colors are applied
      const primaryColor = await page.evaluate(() =>
        getComputedStyle(document.documentElement).getPropertyValue('--primary-color')
      )
      expect(primaryColor.trim()).toBe('#FF5722')
    })
  })

  test('should configure editor settings', async ({ page }) => {
    await test.step('Change font settings', async () => {
      // Select editor tab
      await page.click('[data-testid="editor-tab"]')

      // Change font size
      await page.selectOption('[data-testid="font-size-select"]', '16')

      // Change font family
      await page.selectOption('[data-testid="font-family-select"]', 'Monaco')

      // Change line height
      await page.fill('[data-testid="line-height-input"]', '1.8')

      // Apply settings
      await page.click('[data-testid="apply-editor-settings"]')

      // Navigate to editor to verify
      await page.click('[data-testid="nav-editor"]')

      // Verify font settings are applied
      const editorStyles = await page
        .locator('[data-testid="monaco-editor"]')
        .evaluate(el => {
          const styles = window.getComputedStyle(el)
          return {
            fontSize: styles.fontSize,
            fontFamily: styles.fontFamily,
            lineHeight: styles.lineHeight,
          }
        })

      expect(editorStyles.fontSize).toBe('16px')
      expect(editorStyles.fontFamily).toContain('Monaco')
    })

    await test.step('Configure auto-save settings', async () => {
      // Navigate back to settings
      await page.click('[data-testid="nav-settings"]')
      await page.click('[data-testid="editor-tab"]')

      // Change auto-save interval
      await page.fill('[data-testid="autosave-interval-input"]', '5')

      // Toggle auto-save
      await page.click('[data-testid="autosave-toggle"]')

      // Verify setting is saved
      await expect(page.locator('[data-testid="autosave-status"]')).toContainText(
        '自动保存已启用'
      )
    })

    await test.step('Configure word count settings', async () => {
      // Toggle include spaces in word count
      await page.click('[data-testid="include-spaces-toggle"]')

      // Set reading speed
      await page.fill('[data-testid="reading-speed-input"]', '300')

      // Apply settings
      await page.click('[data-testid="apply-editor-settings"]')

      // Verify settings are saved
      await expect(page.locator('[data-testid="settings-saved"]')).toBeVisible()
    })
  })

  test('should manage backup settings', async ({ page }) => {
    await test.step('Configure automatic backup', async () => {
      // Select backup tab
      await page.click('[data-testid="backup-tab"]')

      // Enable automatic backup
      await page.click('[data-testid="auto-backup-toggle"]')

      // Set backup frequency
      await page.selectOption('[data-testid="backup-frequency-select"]', 'daily')

      // Set backup location
      await page.click('[data-testid="choose-backup-location"]')
      // In real app, this would open a file dialog
      // For testing, we'll use a mock path
      await page.fill('[data-testid="backup-path-input"]', '/Users/<USER>/backups')

      // Set number of backups to keep
      await page.fill('[data-testid="backup-count-input"]', '7')

      // Save settings
      await page.click('[data-testid="save-backup-settings"]')

      // Verify settings are saved
      await expect(page.locator('[data-testid="backup-status"]')).toContainText(
        '备份设置已更新'
      )
    })

    await test.step('Create manual backup', async () => {
      // Click backup now button
      await page.click('[data-testid="backup-now-btn"]')

      // Wait for backup to complete
      await expect(page.locator('[data-testid="backup-progress"]')).toBeVisible()
      await expect(page.locator('[data-testid="backup-complete"]')).toBeVisible({
        timeout: 10000,
      })

      // Verify backup is listed
      await expect(
        page.locator('[data-testid="backup-list"] [data-testid="backup-item"]')
      ).toHaveCount.greaterThan(0)
    })

    await test.step('Restore from backup', async () => {
      // Select a backup
      await page.click('[data-testid="backup-item"]:first-child')

      // Click restore button
      await page.click('[data-testid="restore-backup-btn"]')

      // Confirm restoration
      await page.click('[data-testid="confirm-restore"]')

      // Wait for restoration to complete
      await expect(page.locator('[data-testid="restore-progress"]')).toBeVisible()
      await expect(page.locator('[data-testid="restore-complete"]')).toBeVisible({
        timeout: 10000,
      })
    })
  })

  test('should configure export settings', async ({ page }) => {
    await test.step('Configure default export format', async () => {
      // Select export tab
      await page.click('[data-testid="export-tab"]')

      // Set default format
      await page.selectOption('[data-testid="default-format-select"]', 'markdown')

      // Configure markdown options
      await page.click('[data-testid="include-metadata-toggle"]')
      await page.click('[data-testid="include-toc-toggle"]')

      // Configure file naming
      await page.fill('[data-testid="file-naming-pattern"]', '{novel}_{chapter}_{date}')

      // Save settings
      await page.click('[data-testid="save-export-settings"]')

      // Verify settings are saved
      await expect(page.locator('[data-testid="export-settings-saved"]')).toBeVisible()
    })

    await test.step('Configure DOC export options', async () => {
      // Select DOC options
      await page.click('[data-testid="doc-options-accordion"]')

      // Set page size
      await page.selectOption('[data-testid="page-size-select"]', 'A4')

      // Set margins
      await page.fill('[data-testid="margin-top-input"]', '2.5')
      await page.fill('[data-testid="margin-bottom-input"]', '2.5')
      await page.fill('[data-testid="margin-left-input"]', '3')
      await page.fill('[data-testid="margin-right-input"]', '3')

      // Set font for export
      await page.selectOption('[data-testid="export-font-select"]', 'SimSun')
      await page.fill('[data-testid="export-font-size"]', '12')

      // Save DOC settings
      await page.click('[data-testid="save-doc-settings"]')
    })
  })

  test('should manage keyboard shortcuts', async ({ page }) => {
    await test.step('View current shortcuts', async () => {
      // Select shortcuts tab
      await page.click('[data-testid="shortcuts-tab"]')

      // Verify default shortcuts are displayed
      await expect(
        page.locator('[data-testid="shortcut-item"]')
      ).toHaveCount.greaterThan(0)
      await expect(page.locator('[data-testid="shortcut-save"]')).toContainText(
        'Ctrl+S'
      )
    })

    await test.step('Customize shortcut', async () => {
      // Click to edit save shortcut
      await page.click('[data-testid="edit-shortcut-save"]')

      // Press new key combination
      await page.keyboard.down('Control')
      await page.keyboard.down('Shift')
      await page.keyboard.press('S')
      await page.keyboard.up('Shift')
      await page.keyboard.up('Control')

      // Confirm new shortcut
      await page.click('[data-testid="confirm-shortcut"]')

      // Verify shortcut is updated
      await expect(page.locator('[data-testid="shortcut-save"]')).toContainText(
        'Ctrl+Shift+S'
      )
    })

    await test.step('Reset shortcuts to default', async () => {
      // Click reset button
      await page.click('[data-testid="reset-shortcuts-btn"]')

      // Confirm reset
      await page.click('[data-testid="confirm-reset-shortcuts"]')

      // Verify shortcuts are reset
      await expect(page.locator('[data-testid="shortcut-save"]')).toContainText(
        'Ctrl+S'
      )
    })
  })

  test('should manage data and privacy settings', async ({ page }) => {
    await test.step('Export user data', async () => {
      // Select privacy tab
      await page.click('[data-testid="privacy-tab"]')

      // Click export data button
      const downloadPromise = page.waitForEvent('download')
      await page.click('[data-testid="export-user-data-btn"]')
      const download = await downloadPromise

      // Verify download
      expect(download.suggestedFilename()).toContain('user-data')
      expect(download.suggestedFilename()).toContain('.json')
    })

    await test.step('Clear cache', async () => {
      // Click clear cache button
      await page.click('[data-testid="clear-cache-btn"]')

      // Confirm clearing
      await page.click('[data-testid="confirm-clear-cache"]')

      // Verify cache is cleared
      await expect(page.locator('[data-testid="cache-cleared-message"]')).toContainText(
        '缓存已清除'
      )
    })

    await test.step('Delete all data', async () => {
      // Click delete all data button
      await page.click('[data-testid="delete-all-data-btn"]')

      // Type confirmation
      await page.fill('[data-testid="delete-confirmation-input"]', 'DELETE')

      // Confirm deletion
      await page.click('[data-testid="confirm-delete-all"]')

      // Verify data is deleted and app is reset
      await expect(page.locator('[data-testid="welcome-screen"]')).toBeVisible()
    })
  })

  test('should import and export settings', async ({ page }) => {
    await test.step('Export settings', async () => {
      // Configure some settings first
      await page.click('[data-testid="theme-tab"]')
      await page.click('[data-testid="theme-option-dark"]')

      // Export settings
      await page.click('[data-testid="settings-menu-btn"]')
      await page.click('[data-testid="export-settings-btn"]')

      // Download settings file
      const downloadPromise = page.waitForEvent('download')
      await page.click('[data-testid="confirm-export-settings"]')
      const download = await downloadPromise

      // Verify download
      expect(download.suggestedFilename()).toContain('settings')
      expect(download.suggestedFilename()).toContain('.json')
    })

    await test.step('Import settings', async () => {
      // Reset settings first
      await page.click('[data-testid="reset-settings-btn"]')
      await page.click('[data-testid="confirm-reset-settings"]')

      // Import settings
      await page.click('[data-testid="settings-menu-btn"]')
      await page.click('[data-testid="import-settings-btn"]')

      // Upload settings file
      const fileInput = page.locator('[data-testid="settings-file-input"]')
      await fileInput.setInputFiles('tests/fixtures/test-settings.json')

      // Confirm import
      await page.click('[data-testid="confirm-import-settings"]')

      // Verify settings are imported
      await expect(
        page.locator('[data-testid="settings-imported-message"]')
      ).toContainText('设置已导入')

      // Verify dark theme is applied (from imported settings)
      await expect(page.locator('body')).toHaveClass(/dark-theme/)
    })
  })
})
