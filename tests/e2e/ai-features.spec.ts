import { test, expect } from './fixtures'

test.describe('AI Features', () => {
  test.beforeEach(async ({ page, helpers, testData }) => {
    // Create a novel and navigate to editor
    await helpers.createNovel(testData.novelTitle)
    await page.click(`[data-testid="novel-card"][data-title="${testData.novelTitle}"]`)
    await page.waitForSelector('[data-testid="editor-page"]')
  })

  test('should configure AI settings', async ({ page }) => {
    await test.step('Open AI settings', async () => {
      // Open AI panel
      await page.click('[data-testid="ai-panel-toggle"]')
      await expect(page.locator('[data-testid="ai-panel"]')).toBeVisible()

      // Open settings
      await page.click('[data-testid="ai-settings-btn"]')
      await expect(page.locator('[data-testid="ai-settings-dialog"]')).toBeVisible()
    })

    await test.step('Configure OpenAI provider', async () => {
      // Select OpenAI provider
      await page.selectOption('[data-testid="ai-provider-select"]', 'openai')

      // Enter API key (use test key)
      await page.fill('[data-testid="api-key-input"]', 'test-api-key-12345')

      // Select model
      await page.selectOption('[data-testid="model-select"]', 'gpt-3.5-turbo')

      // Save settings
      await page.click('[data-testid="save-ai-settings"]')

      // Verify settings saved
      await expect(page.locator('[data-testid="ai-status"]')).toContainText(
        'OpenAI 已配置'
      )
    })

    await test.step('Test other AI providers', async () => {
      const providers = [
        { value: 'zhipu', name: '智谱AI' },
        { value: 'qwen', name: '通义千问' },
        { value: 'doubao', name: '豆包' },
      ]

      for (const provider of providers) {
        await page.click('[data-testid="ai-settings-btn"]')
        await page.selectOption('[data-testid="ai-provider-select"]', provider.value)
        await page.fill('[data-testid="api-key-input"]', `test-${provider.value}-key`)
        await page.click('[data-testid="save-ai-settings"]')

        await expect(page.locator('[data-testid="ai-status"]')).toContainText(
          `${provider.name} 已配置`
        )
      }
    })
  })

  test('should use AI for text continuation', async ({ page }) => {
    // Configure AI first (mock mode for testing)
    await page.click('[data-testid="ai-panel-toggle"]')
    await page.click('[data-testid="ai-mock-mode"]') // Enable mock mode for testing

    await test.step('Generate text continuation', async () => {
      // Write some initial content
      const editor = page.locator('[data-testid="monaco-editor"]')
      await editor.click()
      await editor.fill('在一个风雨交加的夜晚，')

      // Request AI continuation
      await page.click('[data-testid="ai-continue-btn"]')

      // Wait for AI response
      await expect(page.locator('[data-testid="ai-loading"]')).toBeVisible()
      await expect(page.locator('[data-testid="ai-loading"]')).not.toBeVisible({
        timeout: 10000,
      })

      // Verify AI generated content appears
      const content = await editor.inputValue()
      expect(content.length).toBeGreaterThan('在一个风雨交加的夜晚，'.length)
    })

    await test.step('Accept AI suggestion', async () => {
      // AI suggestion should appear in preview
      await expect(page.locator('[data-testid="ai-suggestion-preview"]')).toBeVisible()

      // Accept suggestion
      await page.click('[data-testid="accept-ai-suggestion"]')

      // Verify content is inserted into editor
      const editor = page.locator('[data-testid="monaco-editor"]')
      const content = await editor.inputValue()
      expect(content).toContain('在一个风雨交加的夜晚')
    })

    await test.step('Reject AI suggestion', async () => {
      // Generate another suggestion
      await page.click('[data-testid="ai-continue-btn"]')
      await expect(page.locator('[data-testid="ai-suggestion-preview"]')).toBeVisible()

      // Reject suggestion
      await page.click('[data-testid="reject-ai-suggestion"]')

      // Verify preview is closed and content unchanged
      await expect(
        page.locator('[data-testid="ai-suggestion-preview"]')
      ).not.toBeVisible()
    })
  })

  test('should use AI for text refinement', async ({ page }) => {
    await test.step('Refine selected text', async () => {
      // Write some content
      const editor = page.locator('[data-testid="monaco-editor"]')
      await editor.click()
      const originalText = '他走进房间看到桌子上有一本书'
      await editor.fill(originalText)

      // Select all text
      await page.keyboard.press('ControlOrMeta+a')

      // Open AI panel and select refinement
      await page.click('[data-testid="ai-panel-toggle"]')
      await page.click('[data-testid="ai-refine-btn"]')

      // Select refinement type
      await page.selectOption(
        '[data-testid="refine-type-select"]',
        'enhance-description'
      )

      // Apply refinement
      await page.click('[data-testid="apply-refinement"]')

      // Wait for AI processing
      await expect(page.locator('[data-testid="ai-loading"]')).toBeVisible()
      await expect(page.locator('[data-testid="ai-loading"]')).not.toBeVisible({
        timeout: 10000,
      })

      // Verify text has been refined
      const refinedContent = await editor.inputValue()
      expect(refinedContent).not.toBe(originalText)
      expect(refinedContent.length).toBeGreaterThan(originalText.length)
    })

    await test.step('Test different refinement types', async () => {
      const refinementTypes = [
        { value: 'fix-grammar', label: '语法修正' },
        { value: 'enhance-emotion', label: '情感增强' },
        { value: 'simplify', label: '简化表达' },
        { value: 'formal', label: '正式化' },
      ]

      for (const type of refinementTypes) {
        // Select text
        await page.keyboard.press('ControlOrMeta+a')

        // Apply refinement
        await page.selectOption('[data-testid="refine-type-select"]', type.value)
        await page.click('[data-testid="apply-refinement"]')

        // Wait for processing
        await page.waitForTimeout(500) // Mock delay

        // Verify refinement applied
        await expect(page.locator('[data-testid="refinement-status"]')).toContainText(
          `已应用${type.label}`
        )
      }
    })
  })

  test('should generate plot recommendations', async ({ page }) => {
    await test.step('Request plot recommendations', async () => {
      // Write initial plot setup
      const editor = page.locator('[data-testid="monaco-editor"]')
      await editor.click()
      await editor.fill('主角发现了一个神秘的盒子，里面装着一张古老的地图。')

      // Open AI panel
      await page.click('[data-testid="ai-panel-toggle"]')

      // Request plot recommendations
      await page.click('[data-testid="ai-plot-recommend-btn"]')

      // Wait for recommendations
      await expect(page.locator('[data-testid="plot-recommendations"]')).toBeVisible({
        timeout: 10000,
      })

      // Verify recommendations are displayed
      const recommendations = page.locator('[data-testid="plot-recommendation-item"]')
      await expect(recommendations).toHaveCount(3) // Expecting 3 recommendations
    })

    await test.step('Select and apply plot recommendation', async () => {
      // Select first recommendation
      await page.click('[data-testid="plot-recommendation-item"]:first-child')

      // View details
      await expect(page.locator('[data-testid="recommendation-details"]')).toBeVisible()

      // Apply recommendation
      await page.click('[data-testid="apply-recommendation"]')

      // Verify content is added to editor
      const editor = page.locator('[data-testid="monaco-editor"]')
      const content = await editor.inputValue()
      expect(content).toContain('主角发现了一个神秘的盒子')
      expect(content.length).toBeGreaterThan(50) // Should have additional content
    })
  })

  test('should analyze character relationships', async ({ page }) => {
    await test.step('Input character information', async () => {
      // Navigate to character analysis
      await page.click('[data-testid="ai-panel-toggle"]')
      await page.click('[data-testid="character-analysis-tab"]')

      // Add characters
      const characters = [
        { name: '张三', role: '主角', description: '勇敢的冒险家' },
        { name: '李四', role: '导师', description: '睿智的老者' },
        { name: '王五', role: '对手', description: '神秘的反派' },
      ]

      for (const char of characters) {
        await page.click('[data-testid="add-character-btn"]')
        await page.fill('[data-testid="character-name-input"]', char.name)
        await page.fill('[data-testid="character-role-input"]', char.role)
        await page.fill('[data-testid="character-description-input"]', char.description)
        await page.click('[data-testid="save-character"]')
      }
    })

    await test.step('Generate relationship graph', async () => {
      // Request relationship analysis
      await page.click('[data-testid="analyze-relationships-btn"]')

      // Wait for analysis
      await expect(page.locator('[data-testid="relationship-graph"]')).toBeVisible({
        timeout: 10000,
      })

      // Verify graph elements
      await expect(page.locator('[data-testid="character-node"]')).toHaveCount(3)
      await expect(page.locator('[data-testid="relationship-edge"]')).toHaveCount(3) // Minimum expected edges
    })

    await test.step('View relationship details', async () => {
      // Click on a relationship edge
      await page.click('[data-testid="relationship-edge"]:first-child')

      // Verify details panel appears
      await expect(page.locator('[data-testid="relationship-details"]')).toBeVisible()
      await expect(
        page.locator('[data-testid="relationship-description"]')
      ).toContainText(/关系/)
    })
  })

  test('should handle AI errors gracefully', async ({ page }) => {
    await test.step('Handle API key error', async () => {
      // Try to use AI without configuration
      await page.click('[data-testid="ai-panel-toggle"]')
      await page.click('[data-testid="ai-continue-btn"]')

      // Should show configuration required message
      await expect(page.locator('[data-testid="ai-error"]')).toContainText(
        '请先配置AI服务'
      )
    })

    await test.step('Handle API limit error', async () => {
      // Configure with rate-limited test key
      await page.click('[data-testid="ai-settings-btn"]')
      await page.fill('[data-testid="api-key-input"]', 'rate-limited-test-key')
      await page.click('[data-testid="save-ai-settings"]')

      // Try to use AI multiple times quickly
      for (let i = 0; i < 5; i++) {
        await page.click('[data-testid="ai-continue-btn"]')
      }

      // Should show rate limit message
      await expect(page.locator('[data-testid="ai-error"]')).toContainText(/请求频率/i)
    })

    await test.step('Handle network error', async () => {
      // Simulate offline
      await page.context().setOffline(true)

      try {
        await page.click('[data-testid="ai-continue-btn"]')

        // Should show network error
        await expect(page.locator('[data-testid="ai-error"]')).toContainText(/网络/i)
      } finally {
        await page.context().setOffline(false)
      }
    })
  })
})
