import { test, expect } from './fixtures'

test.describe('Novel Creation and Editing Flow', () => {
  test('should create a new novel and edit chapters', async ({
    page,
    testData,
    helpers,
  }) => {
    // Step 1: Create a new novel
    await test.step('Create new novel', async () => {
      await page.click('[data-testid="create-novel-btn"]')

      // Fill in novel details
      await page.fill('[data-testid="novel-title-input"]', testData.novelTitle)
      await page.fill('[data-testid="novel-author-input"]', testData.novelAuthor)
      await page.fill(
        '[data-testid="novel-description-input"]',
        testData.novelDescription
      )

      // Select genre
      await page.click('[data-testid="genre-select"]')
      await page.click('[data-testid="genre-option-fantasy"]')

      // Add tags
      await page.fill('[data-testid="tags-input"]', 'test, e2e, automated')

      // Submit
      await page.click('[data-testid="create-novel-submit"]')

      // Verify novel is created and displayed
      await expect(
        page.locator(`[data-testid="novel-card"][data-title="${testData.novelTitle}"]`)
      ).toBeVisible()
    })

    // Step 2: Enter the editor
    await test.step('Open novel in editor', async () => {
      await page.click(
        `[data-testid="novel-card"][data-title="${testData.novelTitle}"]`
      )
      await page.waitForSelector('[data-testid="editor-page"]')

      // Verify editor is loaded
      await expect(page.locator('[data-testid="monaco-editor"]')).toBeVisible()
      await expect(page.locator('[data-testid="chapter-title"]')).toContainText(
        '新章节'
      )
    })

    // Step 3: Write and save chapter content
    await test.step('Write chapter content', async () => {
      // Set chapter title
      await page.fill('[data-testid="chapter-title-input"]', testData.chapterTitle)

      // Write content in editor
      const editor = page.locator('[data-testid="monaco-editor"]')
      await editor.click()
      await page.keyboard.type(testData.chapterContent)

      // Verify word count updates
      await expect(page.locator('[data-testid="word-count"]')).toContainText(/\d+.*字/)

      // Save manually
      await page.click('[data-testid="save-btn"]')

      // Wait for save to complete
      await expect(page.locator('[data-testid="save-status"]')).toContainText('已保存')
    })

    // Step 4: Test auto-save functionality
    await test.step('Test auto-save', async () => {
      // Type additional content
      await page.keyboard.type(' Additional content for auto-save test.')

      // Wait for auto-save (usually triggers after 3 seconds)
      await page.waitForTimeout(3500)

      // Verify auto-save indicator
      await expect(page.locator('[data-testid="save-status"]')).toContainText('已保存')
    })

    // Step 5: Navigate away and return to verify persistence
    await test.step('Verify data persistence', async () => {
      // Navigate to outline page
      await helpers.navigateTo('outline')

      // Navigate back to editor
      await helpers.navigateTo('editor')

      // Verify content is still there
      const editorContent = await page
        .locator('[data-testid="monaco-editor"]')
        .inputValue()
      expect(editorContent).toContain(testData.chapterContent)
      expect(editorContent).toContain('Additional content for auto-save test.')
    })

    // Step 6: Test keyboard shortcuts
    await test.step('Test keyboard shortcuts', async () => {
      const editor = page.locator('[data-testid="monaco-editor"]')
      await editor.click()

      // Test Ctrl+S / Cmd+S for save
      await page.keyboard.press('ControlOrMeta+s')
      await expect(page.locator('[data-testid="save-status"]')).toContainText('已保存')

      // Test fullscreen mode (F11 or Cmd+Shift+F)
      await page.keyboard.press('F11')
      await expect(page.locator('[data-testid="editor-container"]')).toHaveClass(
        /fullscreen/
      )

      // Exit fullscreen
      await page.keyboard.press('Escape')
      await expect(page.locator('[data-testid="editor-container"]')).not.toHaveClass(
        /fullscreen/
      )
    })

    // Step 7: Close and reopen app to verify data persistence
    await test.step('Verify persistence after app restart', async () => {
      // This would require restarting the Electron app
      // which is handled differently in Electron E2E tests
      // For now, we'll just verify the data is saved in the database

      // Navigate to home/dashboard
      await page.click('[data-testid="nav-home"]')

      // Verify novel still exists with correct stats
      const novelCard = page.locator(
        `[data-testid="novel-card"][data-title="${testData.novelTitle}"]`
      )
      await expect(novelCard).toBeVisible()
      await expect(novelCard.locator('[data-testid="novel-word-count"]')).toContainText(
        /\d+.*字/
      )
      await expect(
        novelCard.locator('[data-testid="novel-chapter-count"]')
      ).toContainText('1 章节')
    })
  })

  test('should handle multiple chapters', async ({ page, helpers, testData }) => {
    // Create a novel first
    await helpers.createNovel(testData.novelTitle)

    // Open the novel
    await page.click(`[data-testid="novel-card"][data-title="${testData.novelTitle}"]`)

    // Create multiple chapters
    const chapters = [
      { title: '第一章', content: '这是第一章的内容。' },
      { title: '第二章', content: '这是第二章的内容。' },
      { title: '第三章', content: '这是第三章的内容。' },
    ]

    for (const chapter of chapters) {
      await test.step(`Create chapter: ${chapter.title}`, async () => {
        // Create new chapter
        await page.click('[data-testid="new-chapter-btn"]')

        // Set title and content
        await page.fill('[data-testid="chapter-title-input"]', chapter.title)
        const editor = page.locator('[data-testid="monaco-editor"]')
        await editor.click()
        await editor.fill(chapter.content)

        // Save
        await page.click('[data-testid="save-btn"]')
        await expect(page.locator('[data-testid="save-status"]')).toContainText(
          '已保存'
        )
      })
    }

    // Verify all chapters are listed
    await test.step('Verify chapter list', async () => {
      await page.click('[data-testid="chapter-list-toggle"]')

      for (const chapter of chapters) {
        await expect(
          page.locator(`[data-testid="chapter-item"][data-title="${chapter.title}"]`)
        ).toBeVisible()
      }
    })

    // Switch between chapters
    await test.step('Switch between chapters', async () => {
      await page.click(`[data-testid="chapter-item"][data-title="第二章"]`)

      const editorContent = await page
        .locator('[data-testid="monaco-editor"]')
        .inputValue()
      expect(editorContent).toContain('这是第二章的内容')
    })
  })

  test('should handle errors gracefully', async ({ page }) => {
    // Test creating novel with empty title
    await test.step('Try to create novel with empty title', async () => {
      await page.click('[data-testid="create-novel-btn"]')

      // Leave title empty
      await page.fill('[data-testid="novel-author-input"]', 'Test Author')

      // Try to submit
      await page.click('[data-testid="create-novel-submit"]')

      // Verify error message
      await expect(page.locator('[data-testid="error-message"]')).toContainText(
        '标题不能为空'
      )
    })

    // Test network error handling
    await test.step('Handle network errors', async () => {
      // Simulate offline mode
      await page.context().setOffline(true)

      try {
        // Try to save
        await page.click('[data-testid="save-btn"]')

        // Should show offline indicator
        await expect(page.locator('[data-testid="offline-indicator"]')).toBeVisible()
      } finally {
        // Restore online mode
        await page.context().setOffline(false)
      }
    })
  })
})
