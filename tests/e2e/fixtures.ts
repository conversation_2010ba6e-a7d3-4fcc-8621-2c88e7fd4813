import { test as base, Page } from '@playwright/test'
import { ElectronApplication, _electron as electron } from 'playwright'
import path from 'path'

interface TestFixtures {
  electronApp: ElectronApplication
  page: Page
  testData: TestData
  helpers: Helpers
}

interface TestData {
  novelTitle: string
  novelAuthor: string
  novelDescription: string
  chapterTitle: string
  chapterContent: string
  materialTitle: string
  materialContent: string
}

interface Helpers {
  createNovel: (title?: string) => Promise<void>
  navigateTo: (path: string) => Promise<void>
  waitForAutoSave: () => Promise<void>
  takeScreenshot: (name: string) => Promise<void>
}

export const test = base.extend<TestFixtures>({
  electronApp: async ({}, use) => {
    // Launch Electron app
    const electronApp = await electron.launch({
      args: [path.join(__dirname, '../../dist/main.js')],
      env: {
        ...process.env,
        NODE_ENV: 'test',
        E2E_TEST: 'true',
      },
    })

    // Wait for the first window
    await electronApp.firstWindow()

    // Use the app
    await use(electronApp)

    // Close the app
    await electronApp.close()
  },

  page: async ({ electronApp }, use) => {
    const page = await electronApp.firstWindow()

    // Set default timeout
    page.setDefaultTimeout(30000)

    // Wait for app to be ready
    await page.waitForSelector('[data-testid="app-ready"]', { timeout: 60000 })

    await use(page)
  },

  testData: async ({}, use) => {
    const timestamp = Date.now()
    await use({
      novelTitle: `Test Novel ${timestamp}`,
      novelAuthor: `Test Author ${timestamp}`,
      novelDescription: `This is a test novel created at ${timestamp}`,
      chapterTitle: `Chapter ${timestamp}`,
      chapterContent: `This is the content of test chapter ${timestamp}. It contains multiple sentences to test word counting and other features.`,
      materialTitle: `Material ${timestamp}`,
      materialContent: `This is test material content for ${timestamp}`,
    })
  },

  helpers: async ({ page }, use) => {
    const helpers: Helpers = {
      createNovel: async (title?: string) => {
        // Click create novel button
        await page.click('[data-testid="create-novel-btn"]')

        // Fill in novel details
        const novelTitle = title || `Test Novel ${Date.now()}`
        await page.fill('[data-testid="novel-title-input"]', novelTitle)
        await page.fill('[data-testid="novel-author-input"]', 'Test Author')
        await page.fill('[data-testid="novel-description-input"]', 'Test Description')

        // Submit
        await page.click('[data-testid="create-novel-submit"]')

        // Wait for creation to complete
        await page.waitForSelector(
          `[data-testid="novel-item"][data-title="${novelTitle}"]`
        )
      },

      navigateTo: async (path: string) => {
        await page.click(`[data-testid="nav-${path}"]`)
        await page.waitForSelector(`[data-testid="${path}-page"]`)
      },

      waitForAutoSave: async () => {
        // Wait for save indicator to appear and disappear
        await page.waitForSelector('[data-testid="save-indicator"]')
        await page.waitForSelector(
          '[data-testid="save-indicator"][data-status="saved"]'
        )
      },

      takeScreenshot: async (name: string) => {
        await page.screenshot({
          path: `tests/e2e/screenshots/${name}.png`,
          fullPage: true,
        })
      },
    }

    await use(helpers)
  },
})

export { expect } from '@playwright/test'
