import '@testing-library/jest-dom'
import { vi } from 'vitest'

// Mock Electron APIs
global.window = {
  ...global.window,
  require: (module: string) => {
    if (module === 'electron') {
      return {
        ipcRenderer: {
          invoke: vi.fn(),
          on: vi.fn(),
          removeAllListeners: vi.fn(),
        },
      }
    }
    return require(module)
  },
}

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
global.localStorage = localStorageMock

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})
