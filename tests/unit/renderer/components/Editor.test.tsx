import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import Editor from '@/components/editor/Editor'

// Mock Monaco Editor
vi.mock('@monaco-editor/react', () => ({
  default: ({ value, onChange, onMount }: any) => {
    const handleChange = (e: any) => {
      onChange?.(e.target.value)
    }

    // Simulate editor mount
    React.useEffect(() => {
      onMount?.(
        {
          getValue: () => value,
          setValue: (newValue: string) => {
            const textarea = document.querySelector('textarea')
            if (textarea) {
              textarea.value = newValue
              const event = new Event('input', { bubbles: true })
              textarea.dispatchEvent(event)
            }
          },
          focus: vi.fn(),
          getModel: () => ({
            getLineCount: () => value?.split('\n').length || 0,
          }),
        },
        {}
      )
    }, [])

    return (
      <textarea
        data-testid="monaco-editor"
        value={value}
        onChange={handleChange}
        aria-label="Code editor"
      />
    )
  },
}))

// Mock zustand store
const mockNovelStore = {
  currentChapter: {
    id: '1',
    title: '第一章',
    content: '这是第一章的内容',
    wordCount: 7,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  updateChapterContent: vi.fn(),
  saveChapter: vi.fn(),
}

vi.mock('@/stores/novel', () => ({
  useNovelStore: () => mockNovelStore,
}))

describe('Editor Component', () => {
  const user = userEvent.setup()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render editor with initial content', () => {
    render(<Editor />)

    const editor = screen.getByTestId('monaco-editor')
    expect(editor).toBeInTheDocument()
    expect(editor).toHaveValue('这是第一章的内容')
  })

  it('should display chapter title', () => {
    render(<Editor />)

    expect(screen.getByText('第一章')).toBeInTheDocument()
  })

  it('should show word count', () => {
    render(<Editor />)

    expect(screen.getByText(/7.*字/)).toBeInTheDocument()
  })

  it('should update content when typing', async () => {
    render(<Editor />)

    const editor = screen.getByTestId('monaco-editor')
    await user.clear(editor)
    await user.type(editor, '新的内容')

    expect(mockNovelStore.updateChapterContent).toHaveBeenCalledWith('新的内容')
  })

  it('should auto-save content after delay', async () => {
    vi.useFakeTimers()
    render(<Editor />)

    const editor = screen.getByTestId('monaco-editor')
    await user.type(editor, '修改的内容')

    // Fast-forward time to trigger auto-save
    vi.advanceTimersByTime(3000)

    await waitFor(() => {
      expect(mockNovelStore.saveChapter).toHaveBeenCalled()
    })

    vi.useRealTimers()
  })

  it('should show save indicator when saving', async () => {
    render(<Editor />)

    const saveButton = screen.getByRole('button', { name: /保存/ })
    fireEvent.click(saveButton)

    expect(screen.getByText('保存中...')).toBeInTheDocument()

    await waitFor(() => {
      expect(screen.getByText('已保存')).toBeInTheDocument()
    })
  })

  it('should handle keyboard shortcuts', async () => {
    render(<Editor />)

    const editor = screen.getByTestId('monaco-editor')
    editor.focus()

    // Test Ctrl+S / Cmd+S for save
    fireEvent.keyDown(editor, { key: 's', ctrlKey: true })

    expect(mockNovelStore.saveChapter).toHaveBeenCalled()
  })

  it('should toggle fullscreen mode', () => {
    render(<Editor />)

    const fullscreenButton = screen.getByRole('button', { name: /全屏/ })
    fireEvent.click(fullscreenButton)

    const editorContainer = screen.getByTestId('editor-container')
    expect(editorContainer).toHaveClass('fullscreen')

    // Toggle back
    fireEvent.click(fullscreenButton)
    expect(editorContainer).not.toHaveClass('fullscreen')
  })

  it('should display reading time estimate', () => {
    render(<Editor />)

    // Assuming ~200 Chinese characters per minute
    expect(screen.getByText(/预计阅读.*分钟/)).toBeInTheDocument()
  })

  it('should handle empty content gracefully', () => {
    mockNovelStore.currentChapter.content = ''
    mockNovelStore.currentChapter.wordCount = 0

    render(<Editor />)

    expect(screen.getByText(/0.*字/)).toBeInTheDocument()
    expect(screen.getByTestId('monaco-editor')).toHaveValue('')
  })

  it('should show toolbar with formatting options', () => {
    render(<Editor />)

    expect(screen.getByRole('button', { name: /加粗/ })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /斜体/ })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /下划线/ })).toBeInTheDocument()
  })

  it('should support undo/redo operations', () => {
    render(<Editor />)

    const undoButton = screen.getByRole('button', { name: /撤销/ })
    const redoButton = screen.getByRole('button', { name: /重做/ })

    expect(undoButton).toBeInTheDocument()
    expect(redoButton).toBeInTheDocument()

    // Initially disabled if no history
    expect(undoButton).toBeDisabled()
    expect(redoButton).toBeDisabled()
  })

  it('should show find and replace dialog', () => {
    render(<Editor />)

    const findButton = screen.getByRole('button', { name: /查找/ })
    fireEvent.click(findButton)

    expect(screen.getByPlaceholderText('查找内容')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('替换为')).toBeInTheDocument()
  })

  it('should handle text selection and copy', async () => {
    render(<Editor />)

    const editor = screen.getByTestId('monaco-editor') as HTMLTextAreaElement

    // Select some text
    editor.setSelectionRange(0, 3)

    // Copy event
    const copyEvent = new ClipboardEvent('copy', {
      clipboardData: new DataTransfer(),
    })

    fireEvent(editor, copyEvent)

    // Verify selection
    expect(editor.selectionStart).toBe(0)
    expect(editor.selectionEnd).toBe(3)
  })

  it('should support theme switching', () => {
    render(<Editor />)

    const themeButton = screen.getByRole('button', { name: /主题/ })
    fireEvent.click(themeButton)

    const darkThemeOption = screen.getByText('暗色主题')
    fireEvent.click(darkThemeOption)

    const editorContainer = screen.getByTestId('editor-container')
    expect(editorContainer).toHaveClass('dark-theme')
  })

  it('should handle focus mode toggle', () => {
    render(<Editor />)

    const focusModeButton = screen.getByRole('button', { name: /专注模式/ })
    fireEvent.click(focusModeButton)

    // In focus mode, toolbar should be hidden
    expect(screen.queryByRole('toolbar')).not.toBeInTheDocument()

    // Press Escape to exit focus mode
    fireEvent.keyDown(document, { key: 'Escape' })
    expect(screen.getByRole('toolbar')).toBeInTheDocument()
  })
})
