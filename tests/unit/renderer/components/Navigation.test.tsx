import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { BrowserRouter } from 'react-router-dom'
import Navigation from '@/components/common/Navigation'

// Mock React Router
const mockNavigate = vi.fn()
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useLocation: () => ({ pathname: '/editor' }),
  }
})

describe('Navigation Component', () => {
  const renderNavigation = () => {
    return render(
      <BrowserRouter>
        <Navigation />
      </BrowserRouter>
    )
  }

  it('should render all navigation items', () => {
    renderNavigation()

    expect(screen.getByText('编辑器')).toBeInTheDocument()
    expect(screen.getByText('大纲')).toBeInTheDocument()
    expect(screen.getByText('素材库')).toBeInTheDocument()
    expect(screen.getByText('设置')).toBeInTheDocument()
  })

  it('should highlight active navigation item', () => {
    renderNavigation()

    const editorLink = screen.getByText('编辑器').closest('a')
    expect(editorLink).toHaveClass('active')
  })

  it('should navigate to editor page when clicked', () => {
    renderNavigation()

    const editorLink = screen.getByText('编辑器')
    fireEvent.click(editorLink)

    expect(mockNavigate).toHaveBeenCalledWith('/editor')
  })

  it('should navigate to outline page when clicked', () => {
    renderNavigation()

    const outlineLink = screen.getByText('大纲')
    fireEvent.click(outlineLink)

    expect(mockNavigate).toHaveBeenCalledWith('/outline')
  })

  it('should navigate to materials page when clicked', () => {
    renderNavigation()

    const materialsLink = screen.getByText('素材库')
    fireEvent.click(materialsLink)

    expect(mockNavigate).toHaveBeenCalledWith('/materials')
  })

  it('should navigate to settings page when clicked', () => {
    renderNavigation()

    const settingsLink = screen.getByText('设置')
    fireEvent.click(settingsLink)

    expect(mockNavigate).toHaveBeenCalledWith('/settings')
  })

  it('should show tooltips on hover', async () => {
    renderNavigation()

    const editorLink = screen.getByText('编辑器')
    fireEvent.mouseEnter(editorLink)

    // Wait for tooltip to appear
    const tooltip = await screen.findByRole('tooltip')
    expect(tooltip).toBeInTheDocument()
  })

  it('should be keyboard accessible', () => {
    renderNavigation()

    const editorLink = screen.getByText('编辑器')
    editorLink.focus()

    expect(document.activeElement).toBe(editorLink)

    // Simulate keyboard navigation
    fireEvent.keyDown(editorLink, { key: 'Enter' })
    expect(mockNavigate).toHaveBeenCalled()
  })

  it('should support keyboard navigation with Tab', () => {
    renderNavigation()

    const links = screen.getAllByRole('link')

    // Tab through all links
    links.forEach((link, index) => {
      link.focus()
      expect(document.activeElement).toBe(link)

      if (index < links.length - 1) {
        fireEvent.keyDown(link, { key: 'Tab' })
      }
    })
  })

  it('should render icons for each navigation item', () => {
    renderNavigation()

    const icons = screen.getAllByRole('img', { hidden: true })
    expect(icons).toHaveLength(4) // 4 navigation items
  })

  it('should apply correct ARIA attributes', () => {
    renderNavigation()

    const nav = screen.getByRole('navigation')
    expect(nav).toHaveAttribute('aria-label', 'Main navigation')

    const links = screen.getAllByRole('link')
    links.forEach(link => {
      expect(link).toHaveAttribute('aria-current')
    })
  })

  it('should handle responsive behavior', () => {
    // Mock window resize
    global.innerWidth = 500
    global.dispatchEvent(new Event('resize'))

    renderNavigation()

    const nav = screen.getByRole('navigation')
    expect(nav).toHaveClass('mobile')
  })
})
