import { describe, it, expect } from 'vitest'
import {
  countWords,
  countCharacters,
  estimateReadingTime,
  extractKeywords,
  truncateText,
  toTitleCase,
  normalizeWhitespace,
  isMostlyChinese,
} from '@/utils/text'

describe('Text Utils', () => {
  describe('countWords', () => {
    it('should count Chinese characters correctly', () => {
      const text = '这是一个测试文本'
      expect(countWords(text)).toBe(7)
    })

    it('should count English words correctly', () => {
      const text = 'This is a test text'
      expect(countWords(text)).toBe(5)
    })

    it('should count mixed Chinese and English text', () => {
      const text = '这是一个 test 文本 with English'
      expect(countWords(text)).toBe(8)
    })

    it('should return 0 for empty text', () => {
      expect(countWords('')).toBe(0)
      expect(countWords('   ')).toBe(0)
    })
  })

  describe('countCharacters', () => {
    it('should count all characters including spaces', () => {
      const text = 'Hello World 你好'
      expect(count<PERSON>haracters(text)).toBe(13)
    })

    it('should count characters excluding spaces', () => {
      const text = 'Hello World 你好'
      expect(countCharacters(text, false)).toBe(11)
    })

    it('should handle empty text', () => {
      expect(countCharacters('')).toBe(0)
    })
  })

  describe('estimateReadingTime', () => {
    it('should estimate reading time correctly', () => {
      const text = '这是一个测试文本。'.repeat(100) // ~700 characters
      const time = estimateReadingTime(text)
      expect(time).toBeGreaterThan(0)
      expect(time).toBeLessThan(10)
    })

    it('should use custom words per minute', () => {
      const text = 'word '.repeat(300) // 300 words
      expect(estimateReadingTime(text, 150)).toBe(2)
      expect(estimateReadingTime(text, 300)).toBe(1)
    })
  })

  describe('extractKeywords', () => {
    it('should extract keywords from text', () => {
      const text = '小说创作是一个很有趣的过程。创作小说需要灵感和技巧。'
      const keywords = extractKeywords(text, 5)

      expect(keywords).toContain('小说')
      expect(keywords).toContain('创作')
      expect(keywords.length).toBeLessThanOrEqual(5)
    })

    it('should handle English text', () => {
      const text =
        'The quick brown fox jumps over the lazy dog. The fox was very quick.'
      const keywords = extractKeywords(text, 3)

      expect(keywords).toContain('fox')
      expect(keywords).toContain('quick')
    })

    it('should return empty array for empty text', () => {
      expect(extractKeywords('')).toEqual([])
    })
  })

  describe('truncateText', () => {
    it('should truncate text to specified length', () => {
      const text = '这是一个很长的文本需要被截断'
      const result = truncateText(text, 10)

      expect(result.length).toBe(10)
      expect(result).toContain('...')
    })

    it('should not truncate if text is shorter than max length', () => {
      const text = '短文本'
      const result = truncateText(text, 10)

      expect(result).toBe(text)
    })

    it('should use custom ellipsis', () => {
      const text = '这是一个很长的文本'
      const result = truncateText(text, 5, '>>>')

      expect(result).toContain('>>>')
    })
  })

  describe('toTitleCase', () => {
    it('should convert text to title case', () => {
      const text = 'hello world this is a test'
      const result = toTitleCase(text)

      expect(result).toBe('Hello World This Is A Test')
    })

    it('should handle mixed case input', () => {
      const text = 'hElLo WoRlD'
      const result = toTitleCase(text)

      expect(result).toBe('Hello World')
    })
  })

  describe('normalizeWhitespace', () => {
    it('should normalize multiple spaces to single space', () => {
      const text = 'Hello    world   how   are   you'
      const result = normalizeWhitespace(text)

      expect(result).toBe('Hello world how are you')
    })

    it('should trim leading and trailing spaces', () => {
      const text = '   Hello world   '
      const result = normalizeWhitespace(text)

      expect(result).toBe('Hello world')
    })

    it('should handle newlines and tabs', () => {
      const text = 'Hello\nworld\t\thow are you'
      const result = normalizeWhitespace(text)

      expect(result).toBe('Hello world how are you')
    })
  })

  describe('isMostlyChinese', () => {
    it('should return true for mostly Chinese text', () => {
      const text = '这是一个测试文本'
      expect(isMostlyChinese(text)).toBe(true)
    })

    it('should return false for mostly English text', () => {
      const text = 'This is a test text'
      expect(isMostlyChinese(text)).toBe(false)
    })

    it('should return true for mixed text with Chinese majority', () => {
      const text = '这是一个 test with mostly 中文'
      expect(isMostlyChinese(text)).toBe(true)
    })

    it('should return false for empty text', () => {
      expect(isMostlyChinese('')).toBe(false)
    })
  })
})
