import { describe, it, expect, beforeEach } from 'vitest'
import { formatDate, getTimeDifference, isToday, getRelativeTime } from '@/utils/date'

describe('Date Utils', () => {
  let testDate: Date

  beforeEach(() => {
    testDate = new Date('2024-01-15T10:30:00.000Z')
  })

  describe('formatDate', () => {
    it('should format date with short format', () => {
      const result = formatDate(testDate, 'short')
      expect(result).toMatch(/2024年1月15日/)
    })

    it('should format date with long format', () => {
      const result = formatDate(testDate, 'long')
      expect(result).toMatch(/2024年1月15日/)
      expect(result).toMatch(/10:30/)
    })

    it('should handle string date input', () => {
      const result = formatDate('2024-01-15T10:30:00.000Z', 'short')
      expect(result).toMatch(/2024年1月15日/)
    })

    it('should return "Invalid date" for invalid date', () => {
      const result = formatDate('invalid-date', 'short')
      expect(result).toBe('Invalid date')
    })
  })

  describe('getTimeDifference', () => {
    it('should calculate time difference correctly', () => {
      const start = new Date('2024-01-15T10:00:00.000Z')
      const end = new Date('2024-01-15T12:30:45.000Z')

      const result = getTimeDifference(start, end)

      expect(result.days).toBe(0)
      expect(result.hours).toBe(2)
      expect(result.minutes).toBe(30)
      expect(result.seconds).toBe(45)
    })

    it('should handle negative time difference', () => {
      const start = new Date('2024-01-15T12:30:45.000Z')
      const end = new Date('2024-01-15T10:00:00.000Z')

      const result = getTimeDifference(start, end)

      expect(result).toEqual({ days: 0, hours: 0, minutes: 0, seconds: 0 })
    })

    it('should handle string date inputs', () => {
      const start = '2024-01-15T10:00:00.000Z'
      const end = '2024-01-15T12:30:45.000Z'

      const result = getTimeDifference(start, end)

      expect(result.hours).toBe(2)
      expect(result.minutes).toBe(30)
    })
  })

  describe('isToday', () => {
    it("should return true for today's date", () => {
      const today = new Date()
      expect(isToday(today)).toBe(true)
    })

    it('should return false for other dates', () => {
      const yesterday = new Date()
      yesterday.setDate(yesterday.getDate() - 1)

      expect(isToday(yesterday)).toBe(false)
    })

    it('should handle string date input', () => {
      const todayString = new Date().toISOString()
      expect(isToday(todayString)).toBe(true)
    })
  })

  describe('getRelativeTime', () => {
    it('should return "刚刚" for very recent times', () => {
      const now = new Date()
      const recent = new Date(now.getTime() - 30 * 1000) // 30 seconds ago

      const result = getRelativeTime(recent)
      expect(result).toMatch(/秒前/)
    })

    it('should return minutes ago for recent times', () => {
      const now = new Date()
      const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000)

      const result = getRelativeTime(fiveMinutesAgo)
      expect(result).toMatch(/5分钟前/)
    })

    it('should return hours ago for longer times', () => {
      const now = new Date()
      const twoHoursAgo = new Date(now.getTime() - 2 * 60 * 60 * 1000)

      const result = getRelativeTime(twoHoursAgo)
      expect(result).toMatch(/2小时前/)
    })

    it('should handle string date input', () => {
      const now = new Date()
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)

      const result = getRelativeTime(oneHourAgo.toISOString())
      expect(result).toMatch(/1小时前/)
    })
  })
})
