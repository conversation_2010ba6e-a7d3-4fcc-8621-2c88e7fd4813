#!/usr/bin/env node

/**
 * Test Runner Script
 * Provides utilities for running different test suites
 */

const { spawn } = require('child_process')
const path = require('path')
const fs = require('fs')
const chalk = require('chalk')

// Parse command line arguments
const args = process.argv.slice(2)
const command = args[0]
const options = args.slice(1)

// Test configurations
const testConfigs = {
  unit: {
    command: 'vitest',
    args: ['--config', 'vitest.config.unit.ts'],
    description: 'Running unit tests...',
  },
  integration: {
    command: 'vitest',
    args: ['--config', 'vitest.config.integration.ts'],
    description: 'Running integration tests...',
  },
  e2e: {
    command: 'playwright',
    args: ['test'],
    description: 'Running E2E tests...',
  },
  all: {
    sequential: ['unit', 'integration', 'e2e'],
    description: 'Running all test suites...',
  },
  coverage: {
    command: 'vitest',
    args: ['--coverage'],
    description: 'Running tests with coverage...',
  },
  watch: {
    command: 'vitest',
    args: ['--watch'],
    description: 'Running tests in watch mode...',
  },
  performance: {
    command: 'node',
    args: ['scripts/performance-tests.js'],
    description: 'Running performance tests...',
  },
  memory: {
    command: 'node',
    args: ['--expose-gc', 'scripts/memory-tests.js'],
    description: 'Running memory leak tests...',
  },
  a11y: {
    command: 'node',
    args: ['scripts/accessibility-tests.js'],
    description: 'Running accessibility tests...',
  },
}

// Helper functions
function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      ...options,
    })

    child.on('close', (code) => {
      if (code !== 0) {
        reject(new Error(`Command failed with exit code ${code}`))
      } else {
        resolve()
      }
    })

    child.on('error', (err) => {
      reject(err)
    })
  })
}

async function runTest(testName) {
  const config = testConfigs[testName]
  
  if (!config) {
    throw new Error(`Unknown test suite: ${testName}`)
  }

  console.log(chalk.blue(`\n${config.description}\n`))

  if (config.sequential) {
    // Run multiple test suites sequentially
    for (const suite of config.sequential) {
      await runTest(suite)
    }
  } else {
    // Run single test suite
    await runCommand(config.command, [...config.args, ...options])
  }
}

async function runParallel(testNames) {
  const promises = testNames.map(name => runTest(name))
  await Promise.all(promises)
}

// Generate test report
async function generateReport() {
  console.log(chalk.blue('\nGenerating test report...\n'))

  const reportDir = path.join(process.cwd(), 'test-reports')
  
  // Create report directory if it doesn't exist
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true })
  }

  // Collect test results
  const results = {
    timestamp: new Date().toISOString(),
    unit: await getTestResults('coverage/coverage-summary.json'),
    e2e: await getTestResults('playwright-report/results.json'),
    performance: await getTestResults('performance-results.json'),
  }

  // Write combined report
  fs.writeFileSync(
    path.join(reportDir, 'combined-report.json'),
    JSON.stringify(results, null, 2)
  )

  // Generate HTML report
  await generateHTMLReport(results, reportDir)

  console.log(chalk.green(`\n✅ Report generated at: ${reportDir}\n`))
}

async function getTestResults(filePath) {
  try {
    const fullPath = path.join(process.cwd(), filePath)
    if (fs.existsSync(fullPath)) {
      return JSON.parse(fs.readFileSync(fullPath, 'utf8'))
    }
  } catch (err) {
    console.warn(chalk.yellow(`Warning: Could not read ${filePath}`))
  }
  return null
}

async function generateHTMLReport(results, reportDir) {
  const html = `
<!DOCTYPE html>
<html>
<head>
  <title>Test Report - ${new Date().toLocaleDateString()}</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      background: #f5f5f5;
    }
    h1 {
      color: #333;
      border-bottom: 2px solid #4CAF50;
      padding-bottom: 10px;
    }
    .section {
      background: white;
      border-radius: 8px;
      padding: 20px;
      margin: 20px 0;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .metric {
      display: inline-block;
      margin: 10px 20px;
    }
    .metric-label {
      font-size: 12px;
      color: #666;
      text-transform: uppercase;
    }
    .metric-value {
      font-size: 24px;
      font-weight: bold;
      color: #333;
    }
    .status-pass {
      color: #4CAF50;
    }
    .status-fail {
      color: #f44336;
    }
    .status-skip {
      color: #FF9800;
    }
    table {
      width: 100%;
      border-collapse: collapse;
    }
    th, td {
      text-align: left;
      padding: 8px;
      border-bottom: 1px solid #ddd;
    }
    th {
      background: #f0f0f0;
    }
  </style>
</head>
<body>
  <h1>📊 Test Report</h1>
  <p>Generated: ${new Date().toLocaleString()}</p>
  
  <div class="section">
    <h2>Summary</h2>
    ${generateSummaryHTML(results)}
  </div>
  
  <div class="section">
    <h2>Unit Test Coverage</h2>
    ${generateCoverageHTML(results.unit)}
  </div>
  
  <div class="section">
    <h2>E2E Test Results</h2>
    ${generateE2EResultsHTML(results.e2e)}
  </div>
  
  <div class="section">
    <h2>Performance Metrics</h2>
    ${generatePerformanceHTML(results.performance)}
  </div>
</body>
</html>
  `

  fs.writeFileSync(path.join(reportDir, 'index.html'), html)
}

function generateSummaryHTML(results) {
  // Implementation for summary HTML
  return '<p>Summary statistics will be displayed here</p>'
}

function generateCoverageHTML(coverage) {
  if (!coverage) return '<p>No coverage data available</p>'
  
  return `
    <div class="metric">
      <div class="metric-label">Lines</div>
      <div class="metric-value">${coverage.total?.lines?.pct || 0}%</div>
    </div>
    <div class="metric">
      <div class="metric-label">Branches</div>
      <div class="metric-value">${coverage.total?.branches?.pct || 0}%</div>
    </div>
    <div class="metric">
      <div class="metric-label">Functions</div>
      <div class="metric-value">${coverage.total?.functions?.pct || 0}%</div>
    </div>
    <div class="metric">
      <div class="metric-label">Statements</div>
      <div class="metric-value">${coverage.total?.statements?.pct || 0}%</div>
    </div>
  `
}

function generateE2EResultsHTML(e2eResults) {
  if (!e2eResults) return '<p>No E2E test results available</p>'
  
  return '<p>E2E test results will be displayed here</p>'
}

function generatePerformanceHTML(performance) {
  if (!performance) return '<p>No performance data available</p>'
  
  return '<p>Performance metrics will be displayed here</p>'
}

// Main execution
async function main() {
  try {
    if (!command) {
      console.log(chalk.yellow('Usage: npm run test:[suite] [options]'))
      console.log(chalk.yellow('\nAvailable test suites:'))
      Object.keys(testConfigs).forEach(name => {
        console.log(chalk.yellow(`  - ${name}`))
      })
      process.exit(0)
    }

    if (command === 'report') {
      await generateReport()
    } else if (command === 'parallel') {
      const suites = options.length > 0 ? options : ['unit', 'integration']
      await runParallel(suites)
    } else {
      await runTest(command)
    }

    console.log(chalk.green('\n✅ Tests completed successfully!\n'))
  } catch (err) {
    console.error(chalk.red(`\n❌ Test failed: ${err.message}\n`))
    process.exit(1)
  }
}

// Run if executed directly
if (require.main === module) {
  main()
}

module.exports = { runTest, runParallel, generateReport }