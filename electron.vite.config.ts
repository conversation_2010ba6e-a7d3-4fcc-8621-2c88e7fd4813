import { defineConfig } from 'vite'
import { resolve } from 'path'

export default defineConfig({
  root: __dirname,
  build: {
    outDir: 'dist',
    emptyOutDir: false,
    lib: {
      entry: resolve(__dirname, 'src/main/index.ts'),
      formats: ['cjs'],
      fileName: () => 'main.js',
    },
    rollupOptions: {
      external: [
        'electron',
        'path',
        'fs',
        'crypto',
        'os',
        'child_process',
        'stream',
        'util',
        'events',
        'url',
        'http',
        'https',
        'zlib',
        'net',
        'tls',
        'sql.js',
      ],
      output: {
        format: 'cjs',
      },
    },
    minify: false,
    sourcemap: true,
  },
})
