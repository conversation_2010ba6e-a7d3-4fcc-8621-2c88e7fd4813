{"ci": {"collect": {"startServerCommand": "npm run preview", "startServerReadyPattern": "Local:", "url": ["http://localhost:4173/", "http://localhost:4173/editor", "http://localhost:4173/outline", "http://localhost:4173/materials", "http://localhost:4173/settings"], "numberOfRuns": 3, "settings": {"preset": "desktop", "throttling": {"cpuSlowdownMultiplier": 1}}}, "assert": {"preset": "lighthouse:no-pwa", "assertions": {"categories:performance": ["error", {"minScore": 0.8}], "categories:accessibility": ["error", {"minScore": 0.9}], "categories:best-practices": ["error", {"minScore": 0.9}], "categories:seo": ["error", {"minScore": 0.8}], "first-contentful-paint": ["error", {"maxNumericValue": 2000}], "largest-contentful-paint": ["error", {"maxNumericValue": 3000}], "cumulative-layout-shift": ["error", {"maxNumericValue": 0.1}], "total-blocking-time": ["error", {"maxNumericValue": 300}], "max-potential-fid": ["error", {"maxNumericValue": 200}]}}, "upload": {"target": "temporary-public-storage"}}}