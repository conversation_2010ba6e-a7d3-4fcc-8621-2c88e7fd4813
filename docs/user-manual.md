# 用户使用手册

## 目录
- [快速开始](#快速开始)
- [界面概览](#界面概览)
- [基础功能](#基础功能)
- [高级功能](#高级功能)
- [AI助手使用](#ai助手使用)
- [数据管理](#数据管理)
- [设置与配置](#设置与配置)
- [常见问题](#常见问题)

## 快速开始

### 安装应用

1. **下载应用**
   - 访问项目主页下载对应平台的安装包
   - Windows: `.exe` 安装程序
   - macOS: `.dmg` 磁盘映像
   - Linux: `.AppImage` 便携应用

2. **安装步骤**
   - **Windows**: 双击 `.exe` 文件，按照向导安装
   - **macOS**: 打开 `.dmg` 文件，将应用拖拽到 Applications 文件夹
   - **Linux**: 给 `.AppImage` 文件执行权限，双击运行

3. **启动应用**
   - 双击桌面图标或从开始菜单启动
   - 首次启动会进行初始化配置

### 创建第一部小说

1. **新建小说**
   - 点击"新建小说"按钮
   - 填写小说基本信息（标题、作者、简介等）
   - 选择小说类型和标签
   - 点击"创建"完成

2. **开始写作**
   - 创建完成后自动进入编辑器
   - 可以开始输入正文内容
   - 系统会自动保存您的文字

## 界面概览

### 主界面布局

```
┌─────────────────────────────────────────────────────────────┐
│  标题栏                                                     │
├─────────────────────────────────────────────────────────────┤
│  工具栏  │                                                   │
│  [新建]  │  主编辑区域                                        │
│  [打开]  │                                                   │
│  [保存]  │  这里是主要的编辑区域，您可以在此输入和编辑文本    │
│  [设置]  │                                                   │
├─────────────────────────────────────────────────────────────┤
│  侧边栏  │  状态栏                                           │
│  大纲    │  字数: 1,234  |  自动保存已启用  |  在线         │
│  角色    │                                                   │
│  素材    │                                                   │
└─────────────────────────────────────────────────────────────┘
```

### 主要区域说明

#### 1. 标题栏
- 显示当前打开的文件名
- 包含窗口控制按钮（最小化、最大化、关闭）
- 显示当前保存状态

#### 2. 工具栏
- **新建**: 创建新文档或新小说
- **打开**: 打开已存在的文件
- **保存**: 手动保存当前文档
- **设置**: 打开设置面板
- **导出**: 导出当前文档

#### 3. 主编辑区域
- 主要的文本编辑区域
- 支持富文本格式
- 实时显示字数统计
- 支持全屏模式

#### 4. 侧边栏
- **大纲**: 显示小说结构大纲
- **角色**: 角色信息管理
- **素材**: 素材库管理
- **AI助手**: AI 功能面板

#### 5. 状态栏
- 显示当前字数统计
- 显示自动保存状态
- 显示网络连接状态
- 显示当前编辑模式

## 基础功能

### 文档操作

#### 创建新文档
1. 点击工具栏的"新建"按钮
2. 选择"新建文档"或"新建小说"
3. 填写文档信息
4. 点击"创建"完成

#### 打开文档
1. 点击工具栏的"打开"按钮
2. 在文件浏览器中选择要打开的文档
3. 双击文件或点击"打开"

#### 保存文档
- **自动保存**: 系统每30秒自动保存一次
- **手动保存**: 点击工具栏的"保存"按钮或按 `Ctrl+S`
- **另存为**: 在文件菜单中选择"另存为"

#### 关闭文档
- 点击文档标签的关闭按钮
- 系统会提示保存未保存的更改

### 编辑功能

#### 文本编辑
- **输入文字**: 直接在编辑区域输入
- **删除文字**: 按 `Delete` 或 `Backspace` 键
- **复制粘贴**: 使用 `Ctrl+C`/`Ctrl+V` 或右键菜单
- **撤销重做**: 使用 `Ctrl+Z`/`Ctrl+Y` 或工具栏按钮

#### 文本格式
- **字体设置**: 选择字体、大小、颜色
- **段落格式**: 设置对齐方式、行间距
- **样式应用**: 粗体、斜体、下划线
- **列表**: 有序列表和无序列表

#### 查找替换
1. 按 `Ctrl+F` 打开查找面板
2. 输入要查找的内容
3. 按 `Enter` 查找下一个
4. 使用 `Ctrl+H` 打开替换面板

### 视图操作

#### 界面布局
- **侧边栏**: 点击侧边栏按钮显示/隐藏
- **全屏模式**: 按 `F11` 或点击全屏按钮
- **专注模式**: 隐藏所有干扰元素
- **分栏显示**: 支持多栏并排显示

#### 缩放操作
- **放大**: 按 `Ctrl++` 或使用工具栏
- **缩小**: 按 `Ctrl+-` 或使用工具栏
- **重置**: 按 `Ctrl+0` 重置到默认大小

## 高级功能

### 小说管理

#### 创建小说项目
1. 点击"新建小说"
2. 填写小说信息：
   - 标题：小说名称
   - 作者：作者姓名
   - 简介：小说简介
   - 类型：选择小说类型
   - 标签：添加相关标签
3. 点击"创建"完成

#### 章节管理
1. 在大纲视图中点击"添加章节"
2. 填写章节标题
3. 设置章节序号
4. 可选：填写章节简介
5. 点击"确定"创建

#### 角色管理
1. 切换到角色标签页
2. 点击"添加角色"
3. 填写角色信息：
   - 姓名：角色姓名
   - 别名：角色别名
   - 年龄：角色年龄
   - 性别：角色性别
   - 性格：性格特点
   - 背景：角色背景
4. 点击"保存"完成

### 大纲构建

#### 创建大纲
1. 在大纲视图中选择"新建大纲"
2. 选择大纲类型：
   - 章节大纲：按章节组织
   - 情节大纲：按情节发展
   - 人物大纲：按人物关系
3. 填写大纲内容
4. 设置层级关系

#### 大纲操作
- **添加节点**: 在现有节点下添加子节点
- **编辑节点**: 双击节点进行编辑
- **删除节点**: 右键点击删除节点
- **拖拽排序**: 拖拽节点调整顺序
- **展开折叠**: 点击箭头展开或折叠

### 素材管理

#### 添加素材
1. 切换到素材标签页
2. 点击"添加素材"
3. 选择素材类型：
   - 文本素材：笔记、灵感等
   - 图片素材：参考图片
   - 网页素材：网页链接
   - 文件素材：参考资料
4. 填写素材信息
5. 点击"保存"

#### 素材分类
- **按类型分类**: 文本、图片、网页、文件
- **按标签分类**: 使用标签组织素材
- **按项目分类**: 关联到特定小说
- **按时间分类**: 按创建时间排序

#### 素材搜索
- **关键词搜索**: 在搜索框输入关键词
- **标签筛选**: 点击标签进行筛选
- **类型筛选**: 按素材类型筛选
- **时间筛选**: 按时间范围筛选

## AI助手使用

### AI功能概览

AI助手提供以下功能：
- **智能续写**: 基于上下文续写文本
- **文本润色**: 优化文本表达和语法
- **剧情推荐**: 推荐情节发展方向
- **角色分析**: 分析角色关系
- **错字纠正**: 自动纠正错别字

### 配置AI服务

#### 添加AI提供商
1. 打开设置面板
2. 选择"AI服务"标签
3. 点击"添加提供商"
4. 选择提供商类型：
   - OpenAI
   - 智谱AI
   - 通义千问
   - 豆包
5. 输入API密钥
6. 测试连接
7. 保存配置

#### 设置默认AI
1. 在AI服务设置中
2. 选择一个已配置的提供商
3. 点击"设为默认"
4. 系统将优先使用默认AI

### 使用AI续写

#### 基础续写
1. 在编辑器中输入一些文本
2. 将光标放在需要续写的位置
3. 点击AI助手面板的"续写"按钮
4. AI将基于上下文生成续写内容
5. 选择接受或重新生成

#### 高级续写
1. 选择续写类型：
   - 自然续写：自然的文本延续
   - 情节发展：推动故事情节
   - 对话生成：生成角色对话
   - 描写增强：增加场景描写
2. 设置续写长度
3. 选择写作风格
4. 点击"生成"

### 文本润色

#### 基础润色
1. 选择需要润色的文本
2. 点击"润色"按钮
3. 选择润色类型：
   - 语法检查：修正语法错误
   - 风格优化：改善写作风格
   - 表达提升：增强表达效果
4. 查看润色结果
5. 接受或拒绝修改

#### 批量润色
1. 选择多个段落或全文
2. 点击"批量润色"
3. 选择润色范围和类型
4. 系统自动处理全文
5. 逐个确认修改

### 剧情推荐

#### 获取推荐
1. 在AI助手面板选择"剧情推荐"
2. 输入当前剧情描述
3. 选择小说类型
4. 涉及的角色（可选）
5. 点击"生成推荐"
6. 查看推荐的剧情方向

#### 应用推荐
1. 查看生成的剧情推荐
2. 选择感兴趣的推荐
3. 点击"应用到文档"
4. 系统将推荐内容插入文档
5. 可以进一步编辑和完善

### 角色关系分析

#### 分析角色
1. 切换到角色关系标签
2. 点击"分析关系"
3. 系统自动分析角色间关系
4. 生成关系图谱
5. 显示关系描述

#### 管理关系
- **添加关系**: 手动添加角色关系
- **编辑关系**: 修改关系描述
- **删除关系**: 移除不需要的关系
- **查看图谱**: 可视化关系网络

## 数据管理

### 自动保存

#### 保存机制
- **定时保存**: 每30秒自动保存一次
- **触发保存**: 内容变化时自动触发
- **版本管理**: 保留最近10个版本
- **恢复功能**: 可以恢复到之前的版本

#### 恢复版本
1. 在文件菜单中选择"版本历史"
2. 查看保存的版本列表
3. 选择要恢复的版本
4. 点击"恢复"按钮
5. 确认恢复操作

### 数据备份

#### 创建备份
1. 在文件菜单中选择"创建备份"
2. 选择备份范围：
   - 整个项目：备份所有数据
   - 当前小说：仅备份当前小说
   - 选择文件：备份指定文件
3. 选择备份位置
4. 点击"创建备份"

#### 恢复备份
1. 在文件菜单中选择"恢复备份"
2. 浏览到备份文件位置
3. 选择备份文件
4. 点击"恢复"
5. 确认恢复操作

### 数据导出

#### 导出格式
- **Markdown**: 标准Markdown格式
- **TXT**: 纯文本格式
- **HTML**: 网页格式（可在Word中打开）
- **PDF**: PDF文档（需要额外安装）

#### 导出设置
1. 点击"导出"按钮
2. 选择导出格式
3. 设置导出选项：
   - 包含元数据：作者、标题等信息
   - 包含大纲：小说大纲结构
   - 包含角色：角色信息
   - 包含素材：相关素材
4. 选择导出位置
5. 点击"导出"

## 设置与配置

### 界面设置

#### 主题设置
1. 打开设置面板
2. 选择"界面"标签
3. 选择主题：
   - 浅色主题：适合白天使用
   - 深色主题：适合夜间使用
   - 自动主题：跟随系统设置
4. 点击"应用"

#### 字体设置
1. 在界面设置中
2. 设置编辑器字体
3. 调整字体大小
4. 设置行间距
5. 点击"保存"

#### 布局设置
- **侧边栏位置**: 左侧或右侧
- **工具栏显示**: 显示或隐藏工具栏
- **状态栏显示**: 显示或隐藏状态栏
- **全屏模式**: 配置全屏行为

### 编辑器设置

#### 自动保存
1. 打开设置面板
2. 选择"编辑器"标签
3. 配置自动保存：
   - 启用自动保存：开启或关闭
   - 保存间隔：设置保存时间间隔
   - 最大版本数：设置保留的版本数量
4. 点击"保存"

#### 编辑选项
- **拼写检查**: 启用或禁用拼写检查
- **自动换行**: 设置自动换行行为
- **制表符设置**: 设置制表符宽度
- **显示行号**: 显示或隐藏行号

### AI服务设置

#### 管理AI提供商
1. 打开设置面板
2. 选择"AI服务"标签
3. 查看已添加的AI提供商
4. 可以编辑、删除或测试连接
5. 设置默认提供商

#### AI使用设置
- **默认续写长度**: 设置续写字数
- **默认润色级别**: 设置润色强度
- **创意度**: 设置AI的创造性
- **使用限制**: 设置使用限制

### 快捷键设置

#### 查看快捷键
1. 打开设置面板
2. 选择"快捷键"标签
3. 查看所有快捷键列表
4. 可以自定义快捷键

#### 常用快捷键
- `Ctrl+N`: 新建文档
- `Ctrl+O`: 打开文档
- `Ctrl+S`: 保存文档
- `Ctrl+Z`: 撤销
- `Ctrl+Y`: 重做
- `Ctrl+F`: 查找
- `Ctrl+H`: 替换
- `Ctrl+B`: 粗体
- `Ctrl+I`: 斜体
- `Ctrl+U`: 下划线
- `F11`: 全屏模式

## 常见问题

### 安装和启动问题

#### Q: 应用无法启动
**A**: 尝试以下解决方案：
1. 确保系统满足最低要求
2. 重新安装应用
3. 检查杀毒软件是否阻止了应用
4. 以管理员身份运行（Windows）

#### Q: macOS提示无法打开应用
**A**: 这是macOS的安全机制：
1. 打开"系统偏好设置"
2. 选择"安全性与隐私"
3. 点击"通用"标签
4. 在"允许从以下位置下载的应用"中选择"仍要打开"

### 编辑和保存问题

#### Q: 文档没有自动保存
**A**: 检查以下设置：
1. 确认自动保存功能已启用
2. 检查保存间隔设置
3. 确保有足够的磁盘空间
4. 检查文件权限

#### Q: 找不到保存的文件
**A**: 文件默认保存在以下位置：
- Windows: `C:\Users\<USER>\Documents\Novel Creation Manager`
- macOS: `/Users/<USER>/Documents/Novel Creation Manager`
- Linux: `/home/<USER>/Documents/Novel Creation Manager`

### AI功能问题

#### Q: AI功能无法使用
**A**: 检查以下事项：
1. 确保已配置AI服务
2. 检查API密钥是否正确
3. 确认网络连接正常
4. 检查API配额是否用完

#### Q: AI生成内容质量不佳
**A**: 优化AI使用：
1. 提供更详细的上下文
2. 调整创意度设置
3. 尝试不同的AI提供商
4. 优化提示词

### 性能问题

#### Q: 应用运行缓慢
**A**: 优化性能：
1. 关闭不需要的文档
2. 定期清理历史版本
3. 减少自动保存频率
4. 重启应用

#### Q: 内存占用过高
**A**: 管理内存使用：
1. 避免同时打开过多大文档
2. 定期清理素材库
3. 重启应用释放内存
4. 检查系统资源使用情况

### 数据丢失问题

#### Q: 误删了重要内容
**A**: 尝试恢复：
1. 检查版本历史
2. 查看自动备份
3. 从备份文件恢复
4. 使用系统文件恢复工具

#### Q: 如何定期备份数据
**A**: 设置自动备份：
1. 在设置中启用自动备份
2. 设置备份间隔
3. 选择备份位置
4. 定期检查备份文件

### 导出和格式问题

#### Q: 导出的格式不正确
**A**: 检查导出设置：
1. 选择正确的导出格式
2. 检查导出选项设置
3. 确认导出内容完整
4. 尝试不同的导出格式

#### Q: PDF导出失败
**A**: PDF导出需要额外配置：
1. 确保系统支持PDF生成
2. 检查PDF相关依赖
3. 尝试导出为HTML格式
4. 使用在线转换工具

### 网络连接问题

#### Q: AI服务连接失败
**A**: 检查网络连接：
1. 确认网络连接正常
2. 检查防火墙设置
3. 验证API密钥有效
4. 尝试不同的网络环境

#### Q: 自动更新失败
**A**: 手动更新应用：
1. 访问项目主页
2. 下载最新版本
3. 卸载旧版本
4. 安装新版本

## 获取帮助

### 在线帮助
- **用户手册**: 完整的使用文档
- **视频教程**: 操作视频演示
- **常见问题**: 常见问题解答
- **更新日志**: 版本更新信息

### 技术支持
- **问题反馈**: 在GitHub提交Issue
- **功能建议**: 提出新功能建议
- **邮件支持**: 发送邮件至技术支持
- **社区论坛**: 参与用户讨论

### 联系方式
- **项目主页**: https://github.com/your-username/novel-creation-manager
- **问题反馈**: https://github.com/your-username/novel-creation-manager/issues
- **邮件支持**: <EMAIL>

---

希望这本用户手册能帮助您更好地使用小说创作管理器。如有任何问题，请随时联系我们获取帮助。