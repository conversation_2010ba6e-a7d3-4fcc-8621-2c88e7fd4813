# 贡献指南

感谢您对小说创作管理器项目的关注！我们欢迎任何形式的贡献，包括代码、文档、问题报告或功能建议。

## 目录
- [行为准则](#行为准则)
- [如何贡献](#如何贡献)
- [开发环境设置](#开发环境设置)
- [代码规范](#代码规范)
- [提交规范](#提交规范)
- [测试规范](#测试规范)
- [文档贡献](#文档贡献)
- [问题反馈](#问题反馈)
- [功能请求](#功能请求)

## 行为准则

### 我们的承诺
为了营造开放和友好的环境，我们承诺：
- 友好和包容地对待每个人
- 尊重不同的观点和经验
- 耐心接受建设性批评
- 关注社区最大利益

### 不当行为
不当行为包括：
- 使用性暗示语言或图像
- 人身攻击或政治攻击
- 公开或私下骚扰
- 未经明确许可发布他人信息

## 如何贡献

### 报告问题
如果您发现了bug或有改进建议：
1. 搜索现有问题，确保问题未被报告
2. 创建新问题，使用适当的模板
3. 提供详细的信息和复现步骤

### 提交代码
1. Fork 项目仓库
2. 创建功能分支
3. 编写代码和测试
4. 提交更改
5. 创建 Pull Request

### 改进文档
- 修正错误或过时的信息
- 添加缺失的文档
- 改进文档结构和可读性
- 翻译文档到其他语言

### 帮助用户
- 在 Issues 中回答其他用户的问题
- 在 Discussions 中参与讨论
- 分享使用经验和技巧

## 开发环境设置

### 前置要求
- Node.js 18.0+
- npm 8.0+
- Git
- 代码编辑器（推荐 VS Code）

### 克隆仓库
```bash
# 克隆项目
git clone https://github.com/your-username/novel-creation-manager.git
cd novel-creation-manager

# 添加上游仓库
git remote add upstream https://github.com/your-username/novel-creation-manager.git
```

### 安装依赖
```bash
# 安装项目依赖
npm install

# 安装开发依赖
npm install --dev
```

### 配置开发环境
```bash
# 复制环境配置文件
cp .env.example .env

# 启动开发服务器
npm run dev
```

### 验证设置
```bash
# 运行测试
npm test

# 运行所有测试套件
npm run test:all

# 检查代码质量
npm run lint

# 检查类型
npm run type-check

# 检查代码格式
npm run format:check
```

## 代码规范

### TypeScript 规范

#### 命名约定
```typescript
// 文件命名：使用 kebab-case
// my-component.tsx
// database-service.ts

// 类命名：使用 PascalCase
class DatabaseService {
  private db: Database;
  
  constructor() {
    this.db = new Database();
  }
}

// 变量和函数：使用 camelCase
const userName = 'John Doe';
function getUserData() {
  // ...
}

// 常量：使用 UPPER_SNAKE_CASE
const MAX_RETRIES = 3;
const API_BASE_URL = 'https://api.example.com';

// 接口：使用 PascalCase，以 I 开头（可选）
interface UserData {
  id: string;
  name: string;
}

// 类型：使用 PascalCase
type UserStatus = 'active' | 'inactive' | 'pending';
```

#### 类型使用
```typescript
// 始终使用类型注解
function calculateTotal(items: Item[]): number {
  return items.reduce((sum, item) => sum + item.price, 0);
}

// 使用接口定义对象结构
interface Config {
  api: {
    baseUrl: string;
    timeout: number;
  };
  features: {
    ai: boolean;
    export: boolean;
  };
}

// 使用泛型提供类型安全
class Repository<T> {
  private items: T[] = [];
  
  add(item: T): void {
    this.items.push(item);
  }
  
  find(predicate: (item: T) => boolean): T | undefined {
    return this.items.find(predicate);
  }
}
```

#### 错误处理
```typescript
// 使用自定义错误类型
class DatabaseError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'DatabaseError';
  }
}

// 异步函数的错误处理
async function fetchData(id: string): Promise<Data> {
  try {
    const response = await fetch(`/api/data/${id}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error('Failed to fetch data:', error);
    throw new DatabaseError('Failed to fetch data', 'FETCH_ERROR', error);
  }
}
```

### React 规范

#### 组件定义
```typescript
// 使用函数组件和 Hooks
interface UserCardProps {
  user: User;
  onEdit?: (user: User) => void;
  onDelete?: (userId: string) => void;
}

export const UserCard: React.FC<UserCardProps> = ({
  user,
  onEdit,
  onDelete
}) => {
  const [isLoading, setIsLoading] = useState(false);
  
  const handleDelete = async () => {
    setIsLoading(true);
    try {
      await deleteUser(user.id);
      onDelete?.(user.id);
    } catch (error) {
      console.error('Failed to delete user:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="user-card">
      <h3>{user.name}</h3>
      <p>{user.email}</p>
      <div className="actions">
        <button 
          onClick={() => onEdit?.(user)}
          disabled={isLoading}
        >
          编辑
        </button>
        <button 
          onClick={handleDelete}
          disabled={isLoading}
        >
          {isLoading ? '删除中...' : '删除'}
        </button>
      </div>
    </div>
  );
};
```

#### Hooks 使用
```typescript
// 自定义 Hook
function useUserData(userId: string) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    const fetchUser = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const data = await userService.getUser(userId);
        setUser(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };
    
    if (userId) {
      fetchUser();
    }
  }, [userId]);
  
  return { user, loading, error, refetch: () => fetchUser() };
}

// 使用自定义 Hook
function UserProfile({ userId }: { userId: string }) {
  const { user, loading, error } = useUserData(userId);
  
  if (loading) return <div>加载中...</div>;
  if (error) return <div>错误: {error}</div>;
  if (!user) return <div>用户不存在</div>;
  
  return (
    <div>
      <h2>{user.name}</h2>
      <p>{user.email}</p>
    </div>
  );
}
```

### 样式规范

#### Styled Components
```typescript
// 使用 styled-components
import styled from 'styled-components';

interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'small' | 'medium' | 'large';
}

const Button = styled.button<ButtonProps>`
  padding: ${props => {
    switch (props.size) {
      case 'small': return '0.5rem 1rem';
      case 'large': return '1rem 2rem';
      default: return '0.75rem 1.5rem';
    }
  }};
  
  background-color: ${props => {
    switch (props.variant) {
      case 'primary': return props.theme.colors.primary;
      case 'danger': return props.theme.colors.error;
      default: return props.theme.colors.secondary;
    }
  }};
  
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  
  &:hover {
    opacity: 0.9;
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

// 使用组件
const App = () => {
  return (
    <div>
      <Button variant="primary">主要按钮</Button>
      <Button variant="secondary">次要按钮</Button>
      <Button variant="danger">危险按钮</Button>
    </div>
  );
};
```

## 提交规范

### 提交消息格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

### Type 类型
- **feat**: 新功能
- **fix**: 修复bug
- **docs**: 文档更改
- **style**: 代码格式（不影响功能）
- **refactor**: 代码重构
- **test**: 测试相关
- **chore**: 构建过程或辅助工具的变动

### Scope 范围
指定提交影响的范围，如：
- `auth`: 认证相关
- `editor`: 编辑器相关
- `database`: 数据库相关
- `ui`: 用户界面相关

### Subject 主题
简洁描述更改内容，使用现在时态，首字母小写。

### Body 正文
详细描述更改内容，可以包括：
- 更改的原因
- 解决的问题
- 实现的方法

### Footer 页脚
用于记录破坏性更改或关联的Issues。

### 提交示例
```bash
# 新功能
feat(editor): add AI-powered text continuation
- Implement AI text continuation feature
- Add configuration for AI providers
- Support multiple AI models

# 修复bug
fix(database): resolve connection timeout issue
- Increase connection timeout to 30 seconds
- Add retry mechanism for failed connections
- Fix connection leak in error handling

# 文档更新
docs(readme): update installation instructions
- Add Node.js 18 requirement
- Update build commands
- Fix broken links

# 代码重构
refactor(ai): improve error handling
- Centralize error handling in AI service
- Add specific error types for different failures
- Improve error messages for better user experience
```

### 提交前检查
```bash
# 运行测试（必须）
npm test

# 运行所有测试套件（推荐）
npm run test:all

# 代码检查（必须）
npm run lint

# 格式检查（必须）
npm run format:check

# 类型检查（必须）
npm run type-check

# 构建检查（必须）
npm run build

# 测试覆盖率检查（推荐）
npm run test:coverage
```

**注意**: 
- 所有测试必须通过才能提交代码
- Husky会自动运行 lint-staged 进行代码检查
- 建议在提交前运行完整测试套件确保代码质量

## 测试规范

### 测试要求
- **测试覆盖率**: 新代码必须达到 80% 以上的测试覆盖率
- **测试类型**: 根据功能类型编写相应的单元测试、集成测试或E2E测试
- **测试运行**: 所有测试必须在提交前通过
- **测试维护**: 修改代码时同步更新相关测试

### 测试结构
```
tests/
├── unit/              # 单元测试
│   ├── renderer/      # 前端单元测试
│   │   ├── components/    # React组件测试
│   │   ├── stores/        # 状态管理测试
│   │   └── utils/         # 工具函数测试
│   └── main/          # 主进程单元测试
│       └── services/      # 服务测试
├── integration/       # 集成测试
│   ├── main/          # 主进程集成测试
│   │   ├── ai/           # AI服务集成测试
│   │   └── services/     # 服务集成测试
│   └── database/     # 数据库测试
└── e2e/              # 端到端测试
    ├── novel-creation.spec.ts    # 小说创建流程
    ├── ai-features.spec.ts       # AI功能测试
    ├── outline-management.spec.ts # 大纲管理测试
    └── settings.spec.ts          # 设置功能测试
```

### 测试命令
```bash
# 运行单元测试
npm run test:unit

# 运行集成测试
npm run test:integration

# 运行E2E测试
npm run test:e2e

# 运行E2E测试（带界面）
npm run test:e2e:headed

# 运行所有测试
npm run test:all

# 生成覆盖率报告
npm run test:coverage

# 监视模式运行测试
npm run test:watch
```

### 单元测试示例
```typescript
// services/user.service.test.ts
import { UserService } from '../user.service';
import { User } from '../types/user';

describe('UserService', () => {
  let userService: UserService;
  let mockDatabase: any;

  beforeEach(() => {
    mockDatabase = {
      create: jest.fn(),
      find: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    };
    userService = new UserService(mockDatabase);
  });

  describe('createUser', () => {
    it('should create a new user', async () => {
      const userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'> = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'hashedPassword',
      };

      mockDatabase.create.mockResolvedValue('user-id');

      const userId = await userService.createUser(userData);

      expect(userId).toBe('user-id');
      expect(mockDatabase.create).toHaveBeenCalledWith('users', {
        ...userData,
        id: expect.any(String),
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date),
      });
    });

    it('should throw error if email already exists', async () => {
      const userData = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'hashedPassword',
      };

      mockDatabase.find.mockResolvedValue([{
        id: 'existing-id',
        email: '<EMAIL>',
      }]);

      await expect(userService.createUser(userData))
        .rejects.toThrow('Email already exists');
    });
  });
});
```

### 组件测试示例
```typescript
// components/UserCard.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { UserCard } from '../UserCard';
import { User } from '../types/user';

describe('UserCard', () => {
  const mockUser: User = {
    id: '1',
    name: 'John Doe',
    email: '<EMAIL>',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockOnEdit = jest.fn();
  const mockOnDelete = jest.fn();

  beforeEach(() => {
    mockOnEdit.mockClear();
    mockOnDelete.mockClear();
  });

  it('renders user information', () => {
    render(
      <UserCard 
        user={mockUser} 
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    );

    expect(screen.getByText(mockUser.name)).toBeInTheDocument();
    expect(screen.getByText(mockUser.email)).toBeInTheDocument();
  });

  it('calls onEdit when edit button is clicked', () => {
    render(
      <UserCard 
        user={mockUser} 
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    );

    fireEvent.click(screen.getByText('编辑'));
    expect(mockOnEdit).toHaveBeenCalledWith(mockUser);
  });

  it('calls onDelete when delete button is clicked', async () => {
    render(
      <UserCard 
        user={mockUser} 
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    );

    fireEvent.click(screen.getByText('删除'));
    expect(mockOnDelete).toHaveBeenCalledWith(mockUser.id);
  });
});
```

### 集成测试示例
```typescript
// integration/api.test.ts
import request from 'supertest';
import { app } from '../app';
import { DatabaseService } from '../services/database.service';

describe('API Integration', () => {
  let database: DatabaseService;

  beforeAll(async () => {
    database = new DatabaseService(':memory:');
    await database.initialize();
  });

  afterAll(async () => {
    await database.close();
  });

  describe('POST /api/users', () => {
    it('should create a new user', async () => {
      const userData = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'password123',
      };

      const response = await request(app)
        .post('/api/users')
        .send(userData)
        .expect(201);

      expect(response.body).toHaveProperty('id');
      expect(response.body.name).toBe(userData.name);
      expect(response.body.email).toBe(userData.email);
      expect(response.body).not.toHaveProperty('password');
    });

    it('should return 400 for invalid data', async () => {
      const invalidData = {
        name: 'John',
        email: 'invalid-email',
      };

      await request(app)
        .post('/api/users')
        .send(invalidData)
        .expect(400);
    });
  });
});
```

## 文档贡献

### 文档格式
- 使用 Markdown 格式
- 遵循现有的文档结构
- 使用清晰简洁的语言
- 包含必要的代码示例

### 文档更新流程
1. Fork 项目仓库
2. 创建文档分支
3. 更新或创建文档
4. 提交更改
5. 创建 Pull Request

### 文档检查清单
- [ ] 拼写和语法检查
- [ ] 链接有效性检查
- [ ] 代码示例测试
- [ ] 格式一致性检查
- [ ] 版本兼容性检查

## 问题反馈

### 创建 Issue
1. 使用合适的 Issue 模板
2. 提供详细的信息
3. 包含复现步骤
4. 附加相关的日志或截图

### Issue 类型
- **Bug Report**: 报告软件缺陷
- **Feature Request**: 请求新功能
- **Documentation**: 文档相关问题
- **Question**: 技术问题咨询

### 问题报告模板
```markdown
## 问题描述
简要描述问题

## 复现步骤
1. 执行操作A
2. 点击按钮B
3. 观察结果C

## 期望行为
描述期望的结果

## 实际行为
描述实际的结果

## 环境信息
- 操作系统: [例如 macOS 12.0]
- 应用版本: [例如 1.0.0]
- Node.js版本: [例如 18.0.0]

## 其他信息
附加任何其他相关信息
```

## 功能请求

### 功能请求模板
```markdown
## 功能描述
详细描述请求的功能

## 问题背景
说明为什么需要这个功能
描述当前的工作流程或限制

## 建议解决方案
描述期望的实现方式
可以包含UI设计或技术方案

## 替代方案
描述可能的替代解决方案

## 优先级
- 低: nice to have
- 中: 会改善用户体验
- 高: 严重影响用户体验
- 紧急: 阻止正常使用
```

### 功能评估标准
- **用户价值**: 对用户的价值和影响
- **实现复杂度**: 技术实现的难度
- **维护成本**: 长期维护的成本
- **项目一致性**: 与项目整体目标的一致性

## Pull Request 流程

### PR 准备
1. 确保代码遵循项目规范
2. 运行所有测试并确保通过
3. 更新相关文档
4. 添加必要的测试

### PR 描述
```markdown
## 更改描述
简要描述更改内容

## 更改类型
- [ ] Bug修复
- [ ] 新功能
- [ ] 文档更新
- [ ] 重构
- [ ] 性能优化

## 相关Issue
关联相关的Issue编号

## 测试
描述如何测试这些更改

## 截图（如适用）
添加UI更改的截图
```

### PR 审查
- **代码审查**: 检查代码质量和规范
- **功能审查**: 确认功能实现正确
- **测试审查**: 确认测试覆盖率
- **文档审查**: 确认文档更新完整

### PR 合并
- 所有检查通过
- 至少一个维护者批准
- 相关讨论已解决
- CI/CD 流程成功

## 社区参与

### 参与讨论
- 在 GitHub Discussions 中参与讨论
- 回答其他用户的问题
- 分享使用经验和技巧

### 贡献方式
- 代码贡献
- 文档贡献
- 测试贡献
- 设计贡献
- 翻译贡献

### 社区活动
- 参与代码审查
- 组织线上/线下活动
- 分享项目相关文章
- 推广项目

## 致谢

感谢所有贡献者的付出！您的贡献让这个项目变得更好。

### 贡献者荣誉墙
- [@贡献者1](https://github.com/contributor1) - 核心功能开发
- [@贡献者2](https://github.com/contributor2) - 文档改进
- [@贡献者3](https://github.com/contributor3) - Bug修复

### 特别感谢
- 感谢所有提供反馈和建议的用户
- 感谢开源社区的宝贵资源
- 感谢所有维护者和贡献者的辛勤工作

## 联系方式

如果您有任何问题或建议，请通过以下方式联系我们：
- GitHub Issues: [项目Issues页面](https://github.com/your-username/novel-creation-manager/issues)
- 邮箱: <EMAIL>
- QQ群: 123456789

---

再次感谢您对小说创作管理器项目的贡献！我们期待与您一起构建更好的产品。