# 测试文档 (Testing Documentation)

## 📋 概述

本项目采用多层次的测试策略，确保代码质量和应用稳定性。测试框架包括：

- **单元测试**: Vitest + React Testing Library
- **集成测试**: Vitest
- **E2E测试**: Playwright
- **性能测试**: Lighthouse CI
- **代码质量**: ESLint + Prettier + TypeScript

## 🏗️ 测试架构

```
测试金字塔
    /\
   /E2E\     (少量，关键用户流程)
  /------\
 /集成测试\   (中等，模块间交互)
/----------\
/  单元测试  \ (大量，独立功能)
```

## 🚀 快速开始

### 安装依赖
```bash
npm install
```

### 运行所有测试
```bash
npm run test:all
```

### 运行特定测试
```bash
npm run test:unit        # 单元测试
npm run test:integration # 集成测试
npm run test:e2e         # E2E测试
```

## 📝 测试命令详解

### 基础测试命令

| 命令 | 描述 |
|------|------|
| `npm test` | 运行默认测试套件 |
| `npm run test:unit` | 运行单元测试 |
| `npm run test:integration` | 运行集成测试 |
| `npm run test:e2e` | 运行E2E测试（无头模式） |
| `npm run test:e2e:headed` | 运行E2E测试（有头模式） |
| `npm run test:e2e:debug` | 调试E2E测试 |

### 高级测试命令

| 命令 | 描述 |
|------|------|
| `npm run test:coverage` | 生成测试覆盖率报告 |
| `npm run test:watch` | 监视模式运行测试 |
| `npm run test:ui` | 打开Vitest UI界面 |
| `npm run test:all` | 顺序运行所有测试 |
| `npm run test:parallel` | 并行运行测试 |
| `npm run test:report` | 生成综合测试报告 |

### 专项测试命令

| 命令 | 描述 |
|------|------|
| `npm run test:performance` | 性能测试 |
| `npm run test:memory` | 内存泄漏检测 |
| `npm run test:a11y` | 可访问性测试 |

### 代码质量命令

| 命令 | 描述 |
|------|------|
| `npm run lint` | 运行ESLint检查 |
| `npm run lint:fix` | 自动修复ESLint问题 |
| `npm run format` | 格式化代码 |
| `npm run format:check` | 检查代码格式 |
| `npm run type-check` | TypeScript类型检查 |

## 🧪 单元测试

### 文件结构
```
tests/unit/
├── renderer/           # 前端单元测试
│   ├── components/    # 组件测试
│   ├── stores/        # 状态管理测试
│   └── utils/         # 工具函数测试
└── main/              # 主进程单元测试
    └── services/      # 服务测试
```

### 编写单元测试

```typescript
// 示例：工具函数测试
import { describe, it, expect } from 'vitest'
import { formatDate } from '@/utils/date'

describe('formatDate', () => {
  it('should format date correctly', () => {
    const date = new Date('2024-01-15')
    const result = formatDate(date, 'short')
    expect(result).toBe('2024年1月15日')
  })
})
```

### 组件测试

```typescript
// 示例：React组件测试
import { render, screen, fireEvent } from '@testing-library/react'
import Button from '@/components/Button'

describe('Button Component', () => {
  it('should handle click events', () => {
    const handleClick = vi.fn()
    render(<Button onClick={handleClick}>Click me</Button>)
    
    fireEvent.click(screen.getByText('Click me'))
    expect(handleClick).toHaveBeenCalled()
  })
})
```

## 🔗 集成测试

### 测试重点

1. **数据库操作**: 测试服务与数据库的交互
2. **IPC通信**: 测试主进程与渲染进程的通信
3. **AI服务**: 测试AI提供商的集成

### 示例

```typescript
// 示例：数据库服务集成测试
describe('NovelService Integration', () => {
  let service: NovelService
  let db: DatabaseService
  
  beforeEach(() => {
    db = new DatabaseService(':memory:')
    service = new NovelService(db)
  })
  
  it('should create and retrieve novel', async () => {
    const novel = await service.createNovel({ title: 'Test' })
    const retrieved = await service.getNovel(novel.id)
    expect(retrieved.title).toBe('Test')
  })
})
```

## 🎭 E2E测试

### 测试场景

1. **创建和编辑流程**: 完整的小说创建和编辑流程
2. **AI功能流程**: AI辅助写作功能测试
3. **大纲管理流程**: 大纲创建和管理
4. **设置更改流程**: 应用设置修改

### 运行E2E测试

```bash
# 运行所有E2E测试
npm run test:e2e

# 运行特定测试文件
npx playwright test tests/e2e/novel-creation.spec.ts

# 运行特定测试套件
npx playwright test --grep "Novel Creation"

# 调试模式
npm run test:e2e:debug
```

### 查看测试报告

```bash
npx playwright show-report
```

## 📊 测试覆盖率

### 目标覆盖率

- **总体**: ≥ 80%
- **单元测试**: ≥ 90%
- **集成测试**: ≥ 70%
- **E2E测试**: 关键用户流程 100%

### 生成覆盖率报告

```bash
npm run test:coverage
```

### 查看覆盖率报告

报告生成在 `coverage/` 目录：
- `coverage/index.html` - HTML报告
- `coverage/lcov.info` - LCOV格式报告

## 🔄 CI/CD集成

### GitHub Actions工作流

项目配置了以下GitHub Actions工作流：

1. **CI Pipeline** (`.github/workflows/ci.yml`)
   - 代码质量检查
   - 单元测试
   - 集成测试
   - E2E测试
   - 构建验证

2. **Release Pipeline** (`.github/workflows/release.yml`)
   - 自动构建发布包
   - 创建GitHub Release
   - 上传构建产物

3. **Nightly Tests** (`.github/workflows/nightly.yml`)
   - 全面测试套件
   - 性能回归测试
   - 内存泄漏检测
   - 依赖安全检查

### 本地运行CI检查

```bash
# 模拟CI环境运行测试
CI=true npm run test:all
```

## 🛠️ 测试工具

### Vitest配置

配置文件：
- `vitest.config.unit.ts` - 单元测试配置
- `vitest.config.integration.ts` - 集成测试配置

### Playwright配置

配置文件：`playwright.config.ts`

支持的浏览器：
- Chromium
- Firefox
- WebKit
- Electron

### 测试辅助工具

1. **测试运行器**: `scripts/test-runner.js`
2. **性能测试**: `lighthouserc.json`
3. **Git Hooks**: Husky + lint-staged

## 🐛 调试测试

### 调试单元测试

```bash
# 使用Vitest UI
npm run test:ui

# 使用VS Code调试器
# 1. 设置断点
# 2. 运行 "Debug: Vitest Tests" 配置
```

### 调试E2E测试

```bash
# 使用Playwright调试器
npm run test:e2e:debug

# 使用headed模式
npm run test:e2e:headed

# 使用VS Code扩展
# 安装 "Playwright Test for VSCode" 扩展
```

## 📈 性能测试

### Lighthouse CI配置

配置文件：`lighthouserc.json`

性能指标阈值：
- Performance: ≥ 80
- Accessibility: ≥ 90
- Best Practices: ≥ 90
- SEO: ≥ 80

### 运行性能测试

```bash
npm run test:performance
```

## 🔍 故障排除

### 常见问题

1. **测试超时**
   ```bash
   # 增加超时时间
   npx playwright test --timeout=60000
   ```

2. **端口冲突**
   ```bash
   # 使用不同端口
   PORT=3001 npm run test:e2e
   ```

3. **清理测试缓存**
   ```bash
   npm run clean:cache
   npm run clean:all
   ```

### 测试数据管理

测试数据位置：
- `tests/fixtures/` - 测试固定数据
- `tests/mocks/` - 模拟数据
- `tests/helpers/` - 测试辅助函数

## 📚 最佳实践

### 测试命名规范

- **描述性命名**: 描述测试的行为，而不是实现
- **Given-When-Then**: 使用BDD风格的测试描述
- **中文描述**: 可以使用中文描述测试用例

### 测试隔离

- 每个测试应该独立运行
- 使用 `beforeEach` 和 `afterEach` 清理状态
- 避免测试间的依赖

### Mock使用

- 只mock外部依赖
- 保持mock简单
- 使用真实数据结构

### 测试数据

- 使用工厂函数生成测试数据
- 避免硬编码测试数据
- 使用有意义的测试数据

## 🤝 贡献指南

### 添加新测试

1. 确定测试类型（单元/集成/E2E）
2. 在相应目录创建测试文件
3. 编写测试用例
4. 运行测试确保通过
5. 提交代码前运行完整测试套件

### 测试审查清单

- [ ] 测试覆盖了所有关键路径
- [ ] 测试名称清晰描述行为
- [ ] 测试独立且可重复
- [ ] 没有硬编码的等待时间
- [ ] 适当使用了mock
- [ ] 测试运行速度合理

## 📞 获取帮助

如果在测试过程中遇到问题：

1. 查看测试日志：`test-results/` 目录
2. 查看GitHub Actions日志
3. 提交Issue到项目仓库
4. 联系项目维护者

---

最后更新：2024年1月