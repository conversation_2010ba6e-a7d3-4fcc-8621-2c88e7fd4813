# 部署指南

## 目录
- [概述](#概述)
- [环境准备](#环境准备)
- [开发环境部署](#开发环境部署)
- [生产环境构建](#生产环境构建)
- [多平台打包](#多平台打包)
- [自动化部署](#自动化部署)
- [发布流程](#发布流程)
- [故障排除](#故障排除)

## 概述

本文档详细介绍小说创作管理器的部署流程，包括开发环境搭建、生产环境构建、多平台打包以及自动化部署等内容。

### 部署目标

- **开发环境**: 快速搭建本地开发环境
- **生产构建**: 生成优化的生产版本
- **多平台支持**: Windows、macOS、Linux 平台
- **自动化**: 自动化构建和发布流程
- **版本管理**: 语义化版本和更新机制

### 部署架构

```
源代码 → 开发环境 → 构建过程 → 打包 → 发布 → 用户
    ↓         ↓         ↓       ↓       ↓
  Git仓库 → 本地开发 → Vite构建 → Electron → 应用商店/网站
```

## 环境准备

### 系统要求

#### 开发环境
- **操作系统**: Windows 10+, macOS 10.14+, Ubuntu 18.04+
- **Node.js**: 18.0 或更高版本
- **npm**: 8.0 或更高版本
- **Git**: 最新版本
- **磁盘空间**: 至少 2GB 可用空间

#### 构建环境
- **Windows**: Windows 10+ with Build Tools
- **macOS**: Xcode 12+ (for code signing)
- **Linux**: build-essential and related tools

### Node.js 环境配置

#### 安装 Node.js

**macOS (使用 Homebrew):**
```bash
brew install node@18
brew link node@18
```

**Windows (使用 Chocolatey):**
```bash
choco install nodejs --version=18.0.0
```

**Linux (Ubuntu/Debian):**
```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

#### 验证安装
```bash
node --version  # 应该显示 v18.0.0 或更高
npm --version   # 应该显示 8.0.0 或更高
```

### Git 配置

#### 安装 Git

**macOS:**
```bash
brew install git
```

**Windows:**
```bash
choco install git
```

**Linux:**
```bash
sudo apt-get install git
```

#### 配置 Git
```bash
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"
git config --global core.autocrlf true
```

### 其他工具

#### macOS 额外工具
```bash
# 安装 Xcode Command Line Tools
xcode-select --install

# 安装 Homebrew (如果还没有安装)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
```

#### Windows 额外工具
```bash
# 安装 Windows Build Tools
npm install --global --production windows-build-tools

# 安装 Chocolatey (如果还没有安装)
Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
```

#### Linux 额外工具
```bash
# Ubuntu/Debian
sudo apt-get install build-essential libssl-dev

# CentOS/RHEL
sudo yum groupinstall "Development Tools"
sudo yum install openssl-devel
```

## 开发环境部署

### 获取源代码

#### 克隆仓库
```bash
git clone https://github.com/your-username/novel-creation-manager.git
cd novel-creation-manager
```

#### 切换到开发分支
```bash
git checkout develop
```

### 安装依赖

#### 安装项目依赖
```bash
npm install
```

#### 验证依赖安装
```bash
npm list --depth=0
```

### 环境配置

#### 创建环境配置文件
```bash
# 复制环境配置模板
cp .env.example .env

# 编辑环境配置
nano .env
```

#### 环境变量配置
```env
# 应用配置
NODE_ENV=development
VITE_APP_VERSION=1.0.0
VITE_APP_NAME="Novel Creation Manager"

# API配置
VITE_API_BASE_URL=http://localhost:3000
VITE_ENABLE_DEV_TOOLS=true

# AI服务配置（可选）
VITE_DEFAULT_AI_PROVIDER=openai
VITE_ENABLE_AI_FEATURES=true
```

### 启动开发服务器

#### 启动开发环境
```bash
npm run dev
```

#### 验证开发环境
- 打开浏览器访问 `http://localhost:3000`
- 确认应用正常运行
- 检查开发者工具是否正常工作

### 开发工具配置

#### VS Code 配置
```json
// .vscode/settings.json
{
  "typescript.preferences.preferTypeOnlyAutoImports": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  }
}
```

#### 推荐插件
```json
// .vscode/extensions.json
{
  "recommendations": [
    "dbaeumer.vscode-eslint",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "bradlc.vscode-tailwindcss"
  ]
}
```

## 生产环境构建

### 构建准备

#### 清理构建目录
```bash
npm run clean
```

#### 检查代码质量
```bash
npm run lint
npm run type-check
```

### 构建应用

#### 完整构建
```bash
npm run build
```

#### 分步构建
```bash
# 仅构建前端
npm run build:vite

# 仅打包 Electron 应用
npm run build:electron
```

### 构建验证

#### 检查构建输出
```bash
# 查看构建输出
ls -la dist/
ls -la release/

# 检查文件大小
du -sh dist/
du -sh release/
```

#### 本地测试
```bash
# 启动生产版本进行测试
npm run preview

# 或直接运行 Electron 应用
npm run electron:prod
```

## 多平台打包

### macOS 打包

#### 配置签名证书

1. **获取开发者证书**
   - 加入 Apple Developer Program
   - 下载 Developer ID Application 证书

2. **安装证书**
   ```bash
   # 双击 .cer 文件安装到 Keychain
   # 在 Keychain Access 中导出 .p12 文件
   ```

3. **配置 Electron Builder**
   ```json
   // package.json
   "build": {
     "mac": {
       "hardenedRuntime": true,
       "gatekeeperAssess": false,
       "entitlements": "entitlements.mac.plist",
       "entitlementsInherit": "entitlements.mac.plist",
       "extendInfo": {
         "NSDocumentsFolderUsageDescription": "应用需要访问文档文件夹来保存您的作品",
         "NSDownloadsFolderUsageDescription": "应用需要访问下载文件夹来导出文件"
       }
     }
   }
   ```

#### 构建 DMG
```bash
# 构建 Intel 版本
npm run build:mac-x64

# 构建 ARM64 版本
npm run build:mac-arm64

# 构建通用版本
npm run build:mac-universal
```

### Windows 打包

#### 配置代码签名

1. **获取代码签名证书**
   - 从可信的证书颁发机构购买证书
   - 安装证书到本地计算机

2. **配置 Electron Builder**
   ```json
   // package.json
   "build": {
     "win": {
       "certificateFile": "./certificates/windows.pfx",
       "certificatePassword": "your-password",
       "publisherName": "Your Company Name",
       "verifyUpdateCodeSignature": false
     }
   }
   ```

#### 构建 EXE
```bash
# 构建 x64 版本
npm run build:win-x64

# 构建 ARM64 版本
npm run build:win-arm64

# 构建便携版
npm run build:win-portable
```

### Linux 打包

#### 构建 AppImage
```bash
# 构建 x64 版本
npm run build:linux-x64

# 构建 ARM64 版本
npm run build:linux-arm64
```

#### 构建 DEB/RPM
```bash
# 构建 DEB 包 (Ubuntu/Debian)
npm run build:linux-deb

# 构建 RPM 包 (CentOS/RHEL)
npm run build:linux-rpm
```

### 交叉构建

#### 使用 Docker 进行交叉构建
```dockerfile
# Dockerfile.linux
FROM node:18-bullseye

RUN apt-get update && apt-get install -y \
    build-essential \
    libssl-dev \
    rpm \
    fakeroot \
    wine \
    mono-complete \
    nsis

WORKDIR /app
COPY package*.json ./
RUN npm install

COPY . .
RUN npm run build:linux
```

#### 构建命令
```bash
# 构建 Linux 版本
docker build -f Dockerfile.linux -t novel-builder-linux .
docker run -v $(pwd)/release:/app/release novel-builder-linux

# 构建 Windows 版本
docker build -f Dockerfile.windows -t novel-builder-windows .
docker run -v $(pwd)/release:/app/release novel-builder-windows
```

## 自动化部署

### GitHub Actions 配置

#### 基本工作流配置

```yaml
# .github/workflows/build.yml
name: Build and Release

on:
  push:
    tags:
      - 'v*'
  pull_request:
    types: [opened, synchronize]

env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.9'

jobs:
  test:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm test
    
    - name: Run linting
      run: npm run lint
    
    - name: Type check
      run: npm run type-check

  build:
    needs: test
    runs-on: ${{ matrix.os }}
    if: startsWith(github.ref, 'refs/tags/')
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        arch: [x64]
        include:
          - os: macos-latest
            arch: arm64
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build application
      run: npm run build
    
    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: ${{ matrix.os }}-${{ matrix.arch }}
        path: release/
    
    - name: Create Release
      uses: softprops/action-gh-release@v1
      if: startsWith(github.ref, 'refs/tags/')
      with:
        files: |
          release/*.dmg
          release/*.exe
          release/*.AppImage
          release/*.deb
          release/*.rpm
        draft: false
        prerelease: false
        generate_release_notes: true
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
```

#### 多平台并行构建

```yaml
# .github/workflows/parallel-build.yml
name: Parallel Build

on:
  push:
    branches: [main]
  workflow_dispatch:

jobs:
  build-matrix:
    runs-on: ubuntu-latest
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
    steps:
    - id: set-matrix
      run: |
        echo "matrix={\"include\":[$(printf '{"os":"ubuntu-latest","arch":"x64"},{"os":"windows-latest","arch":"x64"},{"os":"macos-latest","arch":"x64"},{"os":"macos-latest","arch":"arm64"}' | sed 's/},{/},{/g' | sed 's/"/\\"/g')]}\" >> $GITHUB_OUTPUT

  build:
    needs: build-matrix
    runs-on: ${{ matrix.os }}
    strategy:
      matrix: ${{ fromJson(needs.build-matrix.outputs.matrix) }}
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build for ${{ matrix.os }}-${{ matrix.arch }}
      run: |
        if [ "${{ matrix.os }}" = "ubuntu-latest" ]; then
          npm run build:linux-${{ matrix.arch }}
        elif [ "${{ matrix.os }}" = "windows-latest" ]; then
          npm run build:win-${{ matrix.arch }}
        elif [ "${{ matrix.os }}" = "macos-latest" ]; then
          npm run build:mac-${{ matrix.arch }}
        fi
    
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: ${{ matrix.os }}-${{ matrix.arch }}
        path: release/
```

### CI/CD 最佳实践

#### 缓存配置
```yaml
- name: Cache node modules
  uses: actions/cache@v3
  with:
    path: ~/.npm
    key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
    restore-keys: |
      ${{ runner.os }}-node-
```

#### 环境变量管理
```yaml
- name: Set up environment
  run: |
    echo "BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ')" >> $GITHUB_ENV
    echo "BUILD_NUMBER=${{ github.run_number }}" >> $GITHUB_ENV
    echo "COMMIT_SHA=${{ github.sha }}" >> $GITHUB_ENV
```

#### 版本管理
```yaml
- name: Extract version
  id: version
  run: echo "VERSION=${GITHUB_REF#refs/tags/v}" >> $GITHUB_OUTPUT
```

### Docker 部署

#### Dockerfile 配置
```dockerfile
# Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app

# 复制 package 文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build:vite

# 生产环境
FROM node:18-alpine AS runtime

WORKDIR /app

# 复制构建文件
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json

# 安装生产依赖
RUN npm ci --only=production --ignore-scripts

# 暴露端口
EXPOSE 3000

# 启动命令
CMD ["npm", "run", "preview"]
```

#### Docker Compose 配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - VITE_API_BASE_URL=https://api.example.com
    volumes:
      - ./data:/app/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped
```

## 发布流程

### 版本管理

#### 语义化版本
```bash
# 版本格式: MAJOR.MINOR.PATCH
# 1.0.0 - 主版本号：不兼容的API修改
# 1.1.0 - 次版本号：向下兼容的功能性新增
# 1.1.1 - 修订号：向下兼容的问题修正
```

#### 版本发布流程
```bash
# 1. 更新版本号
npm version patch/minor/major

# 2. 推送标签
git push origin main --tags

# 3. 触发 CI/CD 构建
# GitHub Actions 会自动构建和发布
```

### 发布到应用商店

#### macOS App Store
1. **准备应用**
   - 代码签名
   - 创建 App Store 记录
   - 准备应用截图和描述

2. **使用 Application Loader**
   ```bash
   # 使用 Xcode 上传应用
   xcodebuild -exportArchive -archivePath build/Novel-Creation-Manager.xcarchive -exportPath build/Release -exportOptionsPlist ExportOptions.plist
   ```

3. **提交审核**
   - 填写应用信息
   - 提交审核
   - 等待审核通过

#### Windows Store
1. **准备应用包**
   - 使用 Windows Application Packaging
   - 创建应用清单

2. **上传到 Partner Center**
   - 创建应用记录
   - 上传应用包
   - 提交审核

#### Linux 软件源
```bash
# 创建 DEB 包
dpkg-deb --build debian-package novel-creation-manager.deb

# 创建 RPM 包
rpmbuild -bb novel-creation-manager.spec

# 上传到 PPA 或 COPR
```

### 自动更新配置

#### Electron Updater 配置
```typescript
// main.ts
import { autoUpdater } from 'electron-updater';
import log from 'electron-log';

autoUpdater.logger = log;
autoUpdater.autoDownload = true;
autoUpdater.autoInstallOnAppQuit = true;

// 检查更新
autoUpdater.checkForUpdatesAndNotify();

// 更新事件
autoUpdater.on('update-available', () => {
  log.info('Update available');
});

autoUpdater.on('update-downloaded', () => {
  log.info('Update downloaded');
});
```

#### 更新服务器配置
```json
{
  "url": "https://your-update-server.com/updates",
  "channel": "latest",
  "platform": "darwin",
  "arch": "x64"
}
```

## 故障排除

### 构建问题

#### Node.js 版本不兼容
```bash
# 检查 Node.js 版本
node --version

# 使用 nvm 切换版本
nvm use 18

# 或安装指定版本
nvm install 18
```

#### 依赖安装失败
```bash
# 清理 npm 缓存
npm cache clean --force

# 删除 node_modules
rm -rf node_modules package-lock.json

# 重新安装
npm install
```

#### TypeScript 编译错误
```bash
# 检查 TypeScript 版本
npm list typescript

# 更新 TypeScript
npm install typescript@latest

# 检查 tsconfig.json
npx tsc --noEmit
```

### 打包问题

#### macOS 签名失败
```bash
# 检查证书
security find-identity -v -p codesigning

# 检查 entitlements
codesign --verify --verbose=4 build/Novel-Creation-Manager.app

# 重新签名
codesign --force --deep --sign "Developer ID Application: Your Name" build/Novel-Creation-Manager.app
```

#### Windows 签名失败
```bash
# 检查证书
certmgr.msc

# 使用 signtool 签名
signtool sign /f certificate.pfx /p password /t http://timestamp.digicert.com build/Novel-Creation-Manager.exe
```

#### Linux 权限问题
```bash
# 设置执行权限
chmod +x build/Novel-Creation-Manager.AppImage

# 检查依赖
ldd build/Novel-Creation-Manager.AppImage
```

### 运行时问题

#### 应用启动失败
```bash
# 检查日志
tail -f ~/.config/Novel-Creation-Manager/logs/main.log

# 运行调试模式
DEBUG=electron* npm run electron:dev
```

#### 数据库问题
```bash
# 检查数据库文件
ls -la ~/.config/Novel-Creation-Manager/database.db

# 检查文件权限
ls -la ~/.config/Novel-Creation-Manager/

# 重建数据库
rm ~/.config/Novel-Creation-Manager/database.db
```

#### AI 服务问题
```bash
# 测试网络连接
curl -I https://api.openai.com/v1/models

# 检查 API 密钥
echo $OPENAI_API_KEY

# 测试 API 调用
curl -H "Authorization: Bearer $OPENAI_API_KEY" https://api.openai.com/v1/models
```

### 性能问题

#### 内存泄漏
```bash
# 使用 Chrome DevTools 检查内存
# 在应用中打开开发者工具
# 切换到 Memory 面板
# 记录堆快照并分析
```

#### 启动速度慢
```bash
# 检查依赖大小
npm ls --depth=0 --prod

# 分析打包大小
npm run analyze
```

### 网络问题

#### 代理设置
```bash
# 设置 npm 代理
npm config set proxy http://proxy.company.com:8080
npm config set https-proxy http://proxy.company.com:8080

# 设置 Git 代理
git config --global http.proxy http://proxy.company.com:8080
git config --global https.proxy http://proxy.company.com:8080
```

#### 防火墙问题
```bash
# 检查端口占用
netstat -an | grep :3000

# 检查防火墙状态
sudo ufw status
```

## 监控和维护

### 应用监控

#### 性能监控
```typescript
// 添加性能监控
const { app, BrowserWindow } = require('electron');

app.whenReady().then(() => {
  const win = new BrowserWindow({
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });

  // 监控渲染进程性能
  win.webContents.on('did-finish-load', () => {
    win.webContents.executeJavaScript(`
      // 监控内存使用
      setInterval(() => {
        const memory = performance.memory;
        console.log('Memory usage:', {
          used: Math.round(memory.usedJSHeapSize / 1024 / 1024) + 'MB',
          total: Math.round(memory.totalJSHeapSize / 1024 / 1024) + 'MB'
        });
      }, 10000);
    `);
  });
});
```

#### 错误监控
```typescript
// 全局错误处理
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  // 发送错误到监控系统
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
```

### 日志管理

#### 配置日志
```typescript
// main.ts
import log from 'electron-log';

log.transports.file.level = 'info';
log.transports.console.level = 'debug';

log.info('Application started');

// 错误日志
log.error('Error occurred:', error);

// 调试日志
log.debug('Debug information:', data);
```

#### 日志轮转
```typescript
// 配置日志轮转
log.transports.file.maxSize = '10M';
log.transports.file.archiveLogByDate = true;
```

### 更新维护

#### 自动更新检查
```typescript
// 定期检查更新
setInterval(() => {
  autoUpdater.checkForUpdates();
}, 24 * 60 * 60 * 1000); // 每24小时检查一次
```

#### 版本兼容性检查
```typescript
// 检查系统版本
const os = require('os');
const { app } = require('electron');

function checkSystemCompatibility() {
  const platform = os.platform();
  const version = os.release();
  
  // 检查最低系统要求
  if (platform === 'darwin' && parseInt(version.split('.')[0]) < 19) {
    // macOS 10.15+
    return false;
  }
  
  if (platform === 'win32' && parseInt(version.split('.')[0]) < 10) {
    // Windows 10+
    return false;
  }
  
  return true;
}
```

---

这个部署指南提供了小说创作管理器的完整部署流程，从环境准备到生产部署，涵盖了各个平台的详细步骤和最佳实践。按照这个指南，您可以成功部署和维护小说创作管理器应用。