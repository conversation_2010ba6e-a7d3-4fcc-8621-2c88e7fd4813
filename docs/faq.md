# 常见问题 (FAQ)

## 目录
- [安装和设置](#安装和设置)
- [功能使用](#功能使用)
- [AI助手](#ai助手)
- [数据管理](#数据管理)
- [性能问题](#性能问题)
- [错误处理](#错误处理)
- [兼容性问题](#兼容性问题)
- [技术支持](#技术支持)

## 安装和设置

### Q: 应用无法启动，提示缺少依赖
**A**: 这通常是因为缺少必要的系统依赖。请尝试以下解决方案：

1. **Windows 用户**：
   - 安装 [Visual C++ Redistributable](https://support.microsoft.com/en-us/topic/the-latest-supported-visual-c-downloads-2647da03-1eea-4433-9aff-95f26a218cc0)
   - 运行 `npm install --global --production windows-build-tools`

2. **macOS 用户**：
   - 安装 Xcode Command Line Tools: `xcode-select --install`
   - 如果是 Apple Silicon Mac，确保使用 Rosetta 2 运行 x64 版本

3. **Linux 用户**：
   ```bash
   # Ubuntu/Debian
   sudo apt-get install build-essential libssl-dev
   
   # CentOS/RHEL
   sudo yum groupinstall "Development Tools"
   sudo yum install openssl-devel
   ```

### Q: macOS 提示"无法打开，因为来自未知开发者"
**A**: 这是 macOS 的安全保护机制：

1. **临时解决方案**：
   - 右键点击应用图标
   - 选择"打开"
   - 点击"打开"按钮确认

2. **永久解决方案**：
   - 打开"系统偏好设置" > "安全性与隐私"
   - 在"通用"标签页中，点击"仍要打开"
   - 或者将应用添加到例外列表

### Q: 如何安装特定版本的应用？
**A**: 您可以通过以下方式安装特定版本：

1. **从发布页面下载**：
   - 访问项目的 [Releases 页面](https://github.com/your-username/novel-creation-manager/releases)
   - 下载所需的版本

2. **使用命令行安装**：
   ```bash
   # 下载特定版本
   wget https://github.com/your-username/novel-creation-manager/releases/download/v1.0.0/Novel-Creation-Manager-1.0.0.dmg
   
   # 安装下载的文件
   open Novel-Creation-Manager-1.0.0.dmg
   ```

### Q: 如何迁移旧版本的数据？
**A**: 数据迁移步骤：

1. **备份旧数据**：
   ```bash
   # 备份数据目录
   cp -r ~/Documents/Novel-Creation-Manager ~/Documents/Novel-Creation-Manager-backup
   ```

2. **安装新版本**：
   - 卸载旧版本
   - 安装新版本

3. **恢复数据**：
   - 将备份的数据复制到新版本的数据目录
   - 或者使用应用内置的数据导入功能

## 功能使用

### Q: 如何创建我的第一部小说？
**A**: 创建小说的步骤：

1. **启动应用**：
   - 双击应用图标启动

2. **创建新小说**：
   - 点击工具栏的"新建"按钮
   - 选择"新建小说"
   - 填写小说信息：
     - 标题：小说名称
     - 作者：您的姓名
     - 简介：小说简介
     - 类型：选择小说类型
     - 标签：添加相关标签

3. **开始写作**：
   - 创建完成后自动进入编辑器
   - 开始输入您的小说内容

### Q: 如何添加章节？
**A**: 添加章节的方法：

1. **在侧边栏选择"大纲"**
2. **点击"添加章节"按钮**
3. **填写章节信息**：
   - 章节标题
   - 章节序号
   - 章节简介（可选）
4. **点击"确定"创建**

### Q: 如何管理角色？
**A**: 角色管理功能：

1. **添加角色**：
   - 切换到"角色"标签页
   - 点击"添加角色"
   - 填写角色信息：
     - 姓名：角色姓名
     - 别名：角色别名（可选）
     - 年龄：角色年龄
     - 性别：角色性别
     - 性格：性格特点
     - 背景：角色背景故事

2. **编辑角色**：
   - 双击角色列表中的角色
   - 修改角色信息
   - 点击"保存"确认更改

3. **删除角色**：
   - 右键点击角色
   - 选择"删除"
   - 确认删除操作

### Q: 如何使用素材库？
**A**: 素材库使用方法：

1. **添加素材**：
   - 切换到"素材"标签页
   - 点击"添加素材"
   - 选择素材类型：
     - 文本素材：笔记、灵感等
     - 图片素材：参考图片
     - 网页素材：网页链接
     - 文件素材：参考资料

2. **组织素材**：
   - 使用标签分类素材
   - 按类型筛选素材
   - 搜索素材内容

3. **使用素材**：
   - 双击素材查看详情
   - 复制素材内容到编辑器
   - 关联素材到特定章节

### Q: 如何导出我的小说？
**A**: 导出小说的步骤：

1. **点击"导出"按钮**
2. **选择导出格式**：
   - Markdown：标准 Markdown 格式
   - TXT：纯文本格式
   - HTML：网页格式（可在 Word 中打开）
   - PDF：PDF 文档（需要额外安装）

3. **设置导出选项**：
   - 包含元数据：作者、标题等信息
   - 包含大纲：小说大纲结构
   - 包含角色：角色信息
   - 包含素材：相关素材

4. **选择导出位置**
5. **点击"导出"完成**

## AI助手

### Q: 如何设置AI服务？
**A**: AI服务设置步骤：

1. **打开设置面板**：
   - 点击工具栏的"设置"按钮
   - 选择"AI服务"标签

2. **添加AI提供商**：
   - 点击"添加提供商"
   - 选择提供商类型：
     - OpenAI
     - 智谱AI
     - 通义千问
     - 豆包

3. **配置提供商**：
   - 输入API密钥
   - 选择模型
   - 设置其他参数
   - 点击"测试连接"
   - 保存配置

### Q: AI功能无法使用怎么办？
**A**: AI功能问题排查：

1. **检查AI服务配置**：
   - 确认已添加AI提供商
   - 验证API密钥正确
   - 确保网络连接正常

2. **检查API配额**：
   - 登录AI提供商网站
   - 检查API使用配额
   - 确保账户余额充足

3. **尝试其他提供商**：
   - 添加多个AI提供商
   - 切换到其他提供商使用

4. **查看错误日志**：
   - 打开应用日志
   - 查看AI相关错误信息

### Q: AI生成的内容质量不好怎么办？
**A**: 优化AI生成质量：

1. **提供更好的上下文**：
   - 在续写前提供更多上下文信息
   - 确保上下文连贯且清晰

2. **调整AI参数**：
   - 提高temperature参数增加创造性
   - 降低temperature参数提高准确性
   - 调整maxTokens控制输出长度

3. **优化提示词**：
   - 使用更具体和详细的提示词
   - 明确指定写作风格和要求
   - 提供示例和参考

4. **尝试不同模型**：
   - 不同的AI模型有不同的特长
   - 文学创作可能需要专门的模型

### Q: AI续写功能如何使用？
**A**: AI续写使用方法：

1. **基础续写**：
   - 在编辑器中输入一些文本
   - 将光标放在需要续写的位置
   - 点击AI助手面板的"续写"按钮
   - AI将基于上下文生成续写内容

2. **高级续写**：
   - 选择续写类型：
     - 自然续写：自然的文本延续
     - 情节发展：推动故事情节
     - 对话生成：生成角色对话
     - 描写增强：增加场景描写

3. **续写设置**：
   - 设置续写长度
   - 选择写作风格
   - 指定角色或场景

## 数据管理

### Q: 如何启用自动保存？
**A**: 自动保存设置：

1. **打开设置面板**
2. **选择"编辑器"标签**
3. **配置自动保存**：
   - 启用自动保存：开启或关闭
   - 保存间隔：设置保存时间间隔（建议30秒）
   - 最大版本数：设置保留的版本数量

### Q: 如何恢复误删的内容？
**A**: 内容恢复方法：

1. **使用版本历史**：
   - 在文件菜单中选择"版本历史"
   - 查看保存的版本列表
   - 选择要恢复的版本
   - 点击"恢复"按钮

2. **使用自动备份**：
   - 检查自动备份文件
   - 从备份文件恢复数据

3. **使用系统恢复**：
   - Windows：使用文件历史记录
   - macOS：使用时间机器
   - Linux：使用文件系统快照

### Q: 如何备份数据？
**A**: 数据备份方法：

1. **手动备份**：
   - 在文件菜单中选择"创建备份"
   - 选择备份范围（整个项目或当前小说）
   - 选择备份位置
   - 点击"创建备份"

2. **自动备份**：
   - 在设置中启用自动备份
   - 设置备份间隔
   - 选择备份位置

3. **完整备份**：
   ```bash
   # 备份数据目录
   cp -r ~/Documents/Novel-Creation-Manager ~/Documents/Novel-Creation-Manager-Backup-$(date +%Y%m%d)
   ```

### Q: 如何在不同设备间同步数据？
**A**: 数据同步方法：

1. **使用云存储**：
   - 将数据目录同步到云存储（如 Dropbox、Google Drive）
   - 在不同设备上访问相同的云存储

2. **手动同步**：
   - 导出小说为文件
   - 将文件传输到其他设备
   - 在其他设备上导入文件

3. **使用版本控制**：
   - 将小说文件纳入Git版本控制
   - 使用GitHub或其他Git服务同步

## 性能问题

### Q: 应用运行缓慢怎么办？
**A**: 性能优化方法：

1. **关闭不必要的文档**：
   - 关闭不使用的文档和标签页
   - 减少同时打开的文件数量

2. **清理缓存和历史**：
   - 清理浏览器缓存
   - 删除不需要的版本历史
   - 清理临时文件

3. **优化设置**：
   - 减少自动保存频率
   - 关闭不必要的功能
   - 禁用动画效果

4. **重启应用**：
   - 定期重启应用释放内存
   - 重启计算机清理系统资源

### Q: 大文档编辑卡顿怎么办？
**A**: 大文档优化：

1. **分割文档**：
   - 将大文档分割成多个章节
   - 使用章节管理功能组织内容

2. **使用专注模式**：
   - 启用专注模式隐藏干扰元素
   - 减少界面元素提高性能

3. **调整编辑器设置**：
   - 关闭语法检查
   - 减少自动补全功能
   - 禁用实时预览

### Q: 内存占用过高怎么办？
**A**: 内存管理：

1. **监控内存使用**：
   - 使用任务管理器查看内存占用
   - 识别内存使用高的组件

2. **清理内存**：
   - 关闭不需要的文档
   - 清理历史记录
   - 重启应用

3. **优化配置**：
   - 减少缓存大小
   - 限制历史记录数量
   - 关闭后台功能

## 错误处理

### Q: 遇到"数据库错误"怎么办？
**A**: 数据库错误处理：

1. **检查数据库文件**：
   ```bash
   # 检查数据库文件是否存在
   ls -la ~/Documents/Novel-Creation-Manager/database.db
   
   # 检查文件权限
   ls -la ~/Documents/Novel-Creation-Manager/
   ```

2. **重建数据库**：
   ```bash
   # 备份现有数据库
   cp ~/Documents/Novel-Creation-Manager/database.db ~/Documents/Novel-Creation-Manager/database.db.backup
   
   # 删除损坏的数据库文件
   rm ~/Documents/Novel-Creation-Manager/database.db
   
   # 重启应用，会自动创建新的数据库
   ```

3. **修复数据库**：
   - 使用SQLite工具修复数据库
   - 恢复最近的备份

### Q: 遇到"网络错误"怎么办？
**A**: 网络错误处理：

1. **检查网络连接**：
   - 测试网络连接是否正常
   - 检查防火墙设置
   - 确认代理设置正确

2. **检查API服务**：
   - 验证API密钥是否正确
   - 检查API服务状态
   - 确认API配额充足

3. **重试操作**：
   - 等待几分钟后重试
   - 重启应用
   - 重启网络设备

### Q: 遇到"文件保存失败"怎么办？
**A**: 文件保存错误处理：

1. **检查磁盘空间**：
   - 确保有足够的磁盘空间
   - 清理不必要的文件

2. **检查文件权限**：
   - 确保对目标目录有写入权限
   - 修改文件权限（Linux/macOS）

3. **更改保存位置**：
   - 选择其他目录保存文件
   - 使用外部存储设备

## 兼容性问题

### Q: 在Windows 7上能否运行？
**A**: Windows 7兼容性：

- **官方支持**：应用最低支持Windows 10
- **替代方案**：
  - 升级到Windows 10或更高版本
  - 使用Web版本（如果有）
  - 在虚拟机中运行新版Windows

### Q: 在Linux上运行遇到问题？
**A**: Linux兼容性：

1. **依赖安装**：
   ```bash
   # Ubuntu/Debian
   sudo apt-get install libgtk-3-0 libnotify4 libnss3 libxss1 libxtst6 xdg-utils libatspi2.0-0 libdrm2 libgbm1 libasound2
   
   # CentOS/RHEL
   sudo yum install gtk3 libnotify nss libXScrnSaver libXtst xdg-utils at-spi2-core libdrm libgbm alsa-lib
   ```

2. **字体问题**：
   ```bash
   sudo apt-get install fonts-liberation
   sudo apt-get install ttf-mscorefonts-installer
   ```

3. **权限问题**：
   ```bash
   # 给AppImage执行权限
   chmod +x Novel-Creation-Manager.AppImage
   ```

### Q: 在Apple Silicon Mac上运行有问题？
**A**: Apple Silicon兼容性：

1. **使用原生版本**：
   - 下载ARM64版本的应用
   - 避免使用x64模拟版本

2. **Rosetta 2设置**：
   ```bash
   # 安装Rosetta 2
   softwareupdate --install-rosetta --agree-to-license
   
   # 使用Rosetta 2运行x64应用
   arch -x86_64 /Applications/Novel-Creation-Manager.app/Contents/MacOS/Novel-Creation-Manager
   ```

## 技术支持

### Q: 如何获取技术支持？
**A**: 技术支持渠道：

1. **在线支持**：
   - GitHub Issues: [项目Issues页面](https://github.com/your-username/novel-creation-manager/issues)
   - 邮件支持: <EMAIL>
   - QQ群: 123456789

2. **文档支持**：
   - 用户手册: [docs/user-manual.md](docs/user-manual.md)
   - API文档: [docs/api.md](docs/api.md)
   - 部署指南: [docs/deployment.md](docs/deployment.md)

3. **社区支持**：
   - GitHub Discussions
   - 用户社区论坛
   - 社交媒体群组

### Q: 如何报告bug？
**A**: Bug报告流程：

1. **搜索现有问题**：
   - 在GitHub Issues中搜索类似问题
   - 确认问题未被报告过

2. **创建新问题**：
   - 使用Bug Report模板
   - 提供详细信息：
     - 问题描述
     - 复现步骤
     - 期望行为
     - 实际行为
     - 环境信息

3. **提供诊断信息**：
   - 应用版本
   - 操作系统版本
   - 错误日志
   - 截图或录屏

### Q: 如何请求新功能？
**A**: 功能请求流程：

1. **检查现有请求**：
   - 在GitHub Issues中搜索类似请求
   - 确认功能未被请求过

2. **创建功能请求**：
   - 使用Feature Request模板
   - 详细描述功能需求
   - 说明使用场景和价值

3. **参与讨论**：
   - 参与功能讨论
   - 提供使用反馈
   - 帮助完善功能设计

### Q: 如何参与项目开发？
**A**: 参与开发的方式：

1. **代码贡献**：
   - Fork项目仓库
   - 创建功能分支
   - 提交代码更改
   - 创建Pull Request

2. **文档贡献**：
   - 改进现有文档
   - 添加缺失文档
   - 翻译文档到其他语言

3. **测试贡献**：
   - 报告发现的bug
   - 测试新功能
   - 提供测试反馈

4. **社区贡献**：
   - 回答用户问题
   - 参与技术讨论
   - 推广项目

### Q: 如何获取最新版本？
**A**: 获取最新版本的方法：

1. **自动更新**：
   - 应用会自动检查更新
   - 在有更新时会提示用户

2. **手动检查更新**：
   - 在帮助菜单中选择"检查更新"
   - 或访问项目Release页面

3. **订阅通知**：
   - 在GitHub上Watch项目
   - 订阅Release通知
   - 关注项目社交媒体

### Q: 如何卸载应用？
**A**: 卸载方法：

1. **Windows**：
   - 通过控制面板卸载
   - 或使用设置中的应用管理

2. **macOS**：
   - 将应用拖拽到废纸篓
   - 清理相关文件

3. **Linux**：
   ```bash
   # 对于AppImage
   rm ~/Applications/Novel-Creation-Manager.AppImage
   
   # 对于DEB包
   sudo apt-get remove novel-creation-manager
   
   # 对于RPM包
   sudo yum remove novel-creation-manager
   ```

4. **清理数据**（可选）：
   ```bash
   # 删除应用数据
   rm -rf ~/Documents/Novel-Creation-Manager
   rm -rf ~/.config/Novel-Creation-Manager
   ```

---

希望这个FAQ能帮助您解决使用小说创作管理器时遇到的问题。如果您的问题没有在这里得到解答，请通过上述技术支持渠道联系我们。