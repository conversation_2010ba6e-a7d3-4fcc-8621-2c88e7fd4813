# API 接口文档

## 目录
- [概述](#概述)
- [数据类型定义](#数据类型定义)
- [数据库接口](#数据库接口)
- [AI服务接口](#ai服务接口)
- [文件系统接口](#文件系统接口)
- [IPC通信接口](#ipc通信接口)
- [状态管理接口](#状态管理接口)
- [错误处理](#错误处理)

## 概述

小说创作管理器提供了一套完整的API接口，用于管理小说创作相关的数据和功能。本文档详细描述了各个模块的接口定义和使用方法。

### 接口分类

1. **数据库接口**: 数据的增删改查操作
2. **AI服务接口**: AI功能的调用和管理
3. **文件系统接口**: 文件和目录操作
4. **IPC通信接口**: 进程间通信
5. **状态管理接口**: 应用状态管理
6. **工具接口**: 通用工具函数

### 接口规范

- 所有接口都使用TypeScript定义
- 异步操作返回Promise
- 错误处理使用try-catch
- 参数验证在调用前进行

## 数据类型定义

### 基础类型

#### Novel（小说）
```typescript
interface Novel {
  id: string
  title: string
  author: string
  description: string
  genre: string[]
  tags: string[]
  status: 'draft' | 'in-progress' | 'completed' | 'published'
  wordCount: number
  chapterCount: number
  createdAt: Date
  updatedAt: Date
  coverImage?: string
  publicationDate?: Date
  isbn?: string
}
```

#### Chapter（章节）
```typescript
interface Chapter {
  id: string
  novelId: string
  title: string
  content: string
  chapterNumber: number
  wordCount: number
  summary?: string
  status: 'draft' | 'in-progress' | 'completed'
  createdAt: Date
  updatedAt: Date
  publishedAt?: Date
  orderIndex: number
}
```

#### Character（角色）
```typescript
interface Character {
  id: string
  novelId: string
  name: string
  alias?: string
  age?: number
  gender: 'male' | 'female' | 'other'
  personality: string
  background: string
  appearance?: string
  relationships: CharacterRelationship[]
  createdAt: Date
  updatedAt: Date
}
```

#### CharacterRelationship（角色关系）
```typescript
interface CharacterRelationship {
  id: string
  characterId: string
  targetCharacterId: string
  relationship: string
  description: string
  strength: number // 1-10 关系强度
  createdAt: Date
  updatedAt: Date
}
```

#### Material（素材）
```typescript
interface Material {
  id: string
  novelId: string
  title: string
  content: string
  type: 'text' | 'image' | 'link' | 'file'
  tags: string[]
  filePath?: string
  url?: string
  source?: string
  createdAt: Date
  updatedAt: Date
}
```

#### Outline（大纲）
```typescript
interface Outline {
  id: string
  novelId: string
  title: string
  content: string
  type: 'chapter' | 'plot' | 'character' | 'world'
  parentId?: string
  level: number
  orderIndex: number
  isExpanded: boolean
  createdAt: Date
  updatedAt: Date
}
```

### AI相关类型

#### AIRequest（AI请求）
```typescript
interface AIRequest {
  prompt: string
  context?: string
  maxTokens?: number
  temperature?: number
  provider?: string
  model?: string
  parameters?: Record<string, any>
}
```

#### AIResponse（AI响应）
```typescript
interface AIResponse {
  content: string
  provider: string
  model: string
  usage: {
    promptTokens: number
    completionTokens: number
    totalTokens: number
  }
  timestamp: Date
  success: boolean
  error?: string
}
```

#### AIProvider（AI提供商）
```typescript
interface AIProvider {
  name: string
  provider: 'openai' | 'zhipu' | 'qwen' | 'doubao'
  model: string
  apiKey: string
  baseUrl?: string
  enabled: boolean
  config: Record<string, any>
  createdAt: Date
  updatedAt: Date
}
```

### 设置类型

#### AppSettings（应用设置）
```typescript
interface AppSettings {
  theme: 'light' | 'dark' | 'auto'
  autoSave: {
    enabled: boolean
    interval: number // 毫秒
  }
  editor: {
    fontSize: number
    fontFamily: string
    lineHeight: number
    wordWrap: boolean
    showLineNumbers: boolean
    spellCheck: boolean
  }
  ai: {
    defaultProvider: string
    autoSuggest: boolean
    maxTokens: number
    temperature: number
  }
  export: {
    defaultFormat: string
    includeMetadata: boolean
    includeOutline: boolean
  }
  backup: {
    autoBackup: boolean
    backupInterval: number
    maxBackups: number
    backupLocation: string
  }
}
```

### 文件类型

#### BackupInfo（备份信息）
```typescript
interface BackupInfo {
  id: string
  novelId: string
  backupPath: string
  backupSize: number
  backupType: 'auto' | 'manual'
  createdAt: Date
  description?: string
}
```

#### ExportOptions（导出选项）
```typescript
interface ExportOptions {
  format: 'markdown' | 'txt' | 'html' | 'pdf'
  includeMetadata: boolean
  includeOutline: boolean
  includeCharacters: boolean
  includeMaterials: boolean
  chapterRange?: {
    start: number
    end: number
  }
  customTemplate?: string
}
```

## 数据库接口

### DatabaseService

数据库服务类，提供所有数据库操作功能。

#### 构造函数
```typescript
class DatabaseService {
  constructor(dbPath?: string)
}
```

#### 基础操作

##### execute(sql: string, params: any[]): Promise<void>
执行SQL语句，用于插入、更新、删除操作。

**参数:**
- `sql`: SQL语句
- `params`: 参数数组

**示例:**
```typescript
await db.execute('INSERT INTO novels (id, title, author) VALUES (?, ?, ?)', 
  [novel.id, novel.title, novel.author]);
```

##### get(sql: string, params: any[]): Promise<any>
查询单条记录。

**参数:**
- `sql`: SQL语句
- `params`: 参数数组

**返回:** 查询结果的第一行记录

**示例:**
```typescript
const novel = await db.get('SELECT * FROM novels WHERE id = ?', [novelId]);
```

##### all(sql: string, params: any[]): Promise<any[]>
查询所有记录。

**参数:**
- `sql`: SQL语句
- `params`: 参数数组

**返回:** 查询结果的所有记录

**示例:**
```typescript
const novels = await db.all('SELECT * FROM novels ORDER BY created_at DESC');
```

##### run(sql: string, params: any[]): Promise<RunResult>
执行SQL语句并返回结果。

**参数:**
- `sql`: SQL语句
- `params`: 参数数组

**返回:** 包含lastID和changes的RunResult对象

**示例:**
```typescript
const result = await db.run('INSERT INTO novels (title, author) VALUES (?, ?)', 
  [title, author]);
const novelId = result.lastID;
```

#### 小说管理

##### createNovel(novel: Omit<Novel, 'id' | 'createdAt' | 'updatedAt'>): Promise<string>
创建新小说。

**参数:**
- `novel`: 小说信息（不包含id、createdAt、updatedAt）

**返回:** 新小说的ID

**示例:**
```typescript
const novelId = await db.createNovel({
  title: '我的小说',
  author: '作者名',
  description: '小说简介',
  genre: ['奇幻'],
  tags: ['冒险'],
  status: 'draft',
  wordCount: 0,
  chapterCount: 0
});
```

##### getNovel(id: string): Promise<Novel | null>
获取小说信息。

**参数:**
- `id`: 小说ID

**返回:** 小说信息或null

##### updateNovel(id: string, updates: Partial<Novel>): Promise<void>
更新小说信息。

**参数:**
- `id`: 小说ID
- `updates`: 要更新的字段

##### deleteNovel(id: string): Promise<void>
删除小说。

**参数:**
- `id`: 小说ID

##### getAllNovels(): Promise<Novel[]>
获取所有小说。

#### 章节管理

##### createChapter(chapter: Omit<Chapter, 'id' | 'createdAt' | 'updatedAt'>): Promise<string>
创建新章节。

##### getChapter(id: string): Promise<Chapter | null>
获取章节信息。

##### updateChapter(id: string, updates: Partial<Chapter>): Promise<void>
更新章节信息。

##### deleteChapter(id: string): Promise<void>
删除章节。

##### getChaptersByNovelId(novelId: string): Promise<Chapter[]>
获取小说的所有章节。

#### 角色管理

##### createCharacter(character: Omit<Character, 'id' | 'createdAt' | 'updatedAt'>): Promise<string>
创建新角色。

##### getCharacter(id: string): Promise<Character | null>
获取角色信息。

##### updateCharacter(id: string, updates: Partial<Character>): Promise<void>
更新角色信息。

##### deleteCharacter(id: string): Promise<void>
删除角色。

##### getCharactersByNovelId(novelId: string): Promise<Character[]>
获取小说的所有角色。

#### 素材管理

##### createMaterial(material: Omit<Material, 'id' | 'createdAt' | 'updatedAt'>): Promise<string>
创建新素材。

##### getMaterial(id: string): Promise<Material | null>
获取素材信息。

##### updateMaterial(id: string, updates: Partial<Material>): Promise<void>
更新素材信息。

##### deleteMaterial(id: string): Promise<void>
删除素材。

##### getMaterialsByNovelId(novelId: string): Promise<Material[]>
获取小说的所有素材。

#### 大纲管理

##### createOutline(outline: Omit<Outline, 'id' | 'createdAt' | 'updatedAt'>): Promise<string>
创建新大纲。

##### getOutline(id: string): Promise<Outline | null>
获取大纲信息。

##### updateOutline(id: string, updates: Partial<Outline>): Promise<void>
更新大纲信息。

##### deleteOutline(id: string): Promise<void>
删除大纲。

##### getOutlinesByNovelId(novelId: string): Promise<Outline[]>
获取小说的所有大纲。

#### 搜索功能

##### searchNovels(query: string): Promise<Novel[]>
搜索小说。

##### searchChapters(novelId: string, query: string): Promise<Chapter[]>
搜索章节。

##### searchMaterials(novelId: string, query: string): Promise<Material[]>
搜索素材。

#### 统计功能

##### getNovelStats(novelId: string): Promise<NovelStats>
获取小说统计信息。

```typescript
interface NovelStats {
  totalWords: number
  totalChapters: number
  totalCharacters: number
  totalMaterials: number
  lastUpdated: Date
  dailyWords: number
  weeklyWords: number
  monthlyWords: number
}
```

## AI服务接口

### AIService

AI服务类，提供所有AI相关功能。

#### 构造函数
```typescript
class AIService {
  constructor()
}
```

#### 基础AI功能

##### generate(request: AIRequest, provider?: string): Promise<AIResponse>
生成AI内容。

**参数:**
- `request`: AI请求对象
- `provider`: 指定AI提供商（可选）

**返回:** AI响应对象

**示例:**
```typescript
const response = await aiService.generate({
  prompt: '请续写以下内容：',
  context: currentText,
  maxTokens: 500,
  temperature: 0.7
});
```

##### generateWithRetry(request: AIRequest, provider?: string, maxRetries?: number): Promise<AIResponse>
带重试机制的AI生成。

**参数:**
- `request`: AI请求对象
- `provider`: 指定AI提供商（可选）
- `maxRetries`: 最大重试次数（默认3次）

##### generateMultiple(request: AIRequest, providers?: string[]): Promise<AIResponse[]>
使用多个AI提供商生成内容。

**参数:**
- `request`: AI请求对象
- `providers`: AI提供商列表（可选，默认使用所有可用提供商）

#### 写作辅助功能

##### continueWriting(context: string, maxLength?: number, style?: string): Promise<string>
智能续写。

**参数:**
- `context`: 上下文文本
- `maxLength`: 最大长度（默认500）
- `style`: 写作风格（可选）

**返回:** 续写内容

**示例:**
```typescript
const continuation = await aiService.continueWriting(
  '这是当前的故事内容...',
  300,
  '文学性'
);
```

##### polishText(text: string, style?: string, focus?: string[]): Promise<string>
文本润色。

**参数:**
- `text`: 要润色的文本
- `style`: 目标风格（默认'formal'）
- `focus`: 关注点数组（默认['grammar', 'style', 'clarity']）

**返回:** 润色后的文本

**示例:**
```typescript
const polished = await aiService.polishText(
  originalText,
  '文学性',
  ['语法', '风格', '表达']
);
```

##### plotRecommendation(currentPlot: string, genre: string, characters?: string[]): Promise<string[]>
剧情推荐。

**参数:**
- `currentPlot`: 当前剧情
- `genre`: 小说类型
- `characters`: 涉及角色（可选）

**返回:** 推荐剧情数组

##### analyzeCharacterRelationships(characters: Array<{name: string, description: string}>, currentStory: string): Promise<Array<CharacterRelationshipAnalysis>>
角色关系分析。

```typescript
interface CharacterRelationshipAnalysis {
  character1: string
  character2: string
  relationship: string
  description: string
  strength: number
}
```

##### correctText(text: string): Promise<string>
错字纠正。

**参数:**
- `text`: 要纠正的文本

**返回:** 纠正后的文本

#### 提供商管理

##### getAvailableProviders(): string[]
获取可用的AI提供商列表。

##### setDefaultProvider(provider: string): void
设置默认AI提供商。

##### getDefaultProvider(): string
获取默认AI提供商。

##### addProvider(config: AIProviderConfig): Promise<void>
添加AI提供商配置。

```typescript
interface AIProviderConfig {
  name: string
  provider: 'openai' | 'zhipu' | 'qwen' | 'doubao'
  model: string
  apiKey: string
  baseUrl?: string
  config?: Record<string, any>
}
```

##### removeProvider(name: string): Promise<void>
移除AI提供商。

##### testProvider(name: string): Promise<boolean>
测试AI提供商连接。

## 文件系统接口

### StorageService

文件存储服务类，提供文件操作和导出功能。

#### 构造函数
```typescript
class StorageService {
  constructor(db: DatabaseService)
}
```

#### 文件操作

##### saveFile(data: any, filePath: string): Promise<boolean>
保存文件。

**参数:**
- `data`: 要保存的数据
- `filePath`: 文件路径

**返回:** 保存是否成功

##### readFile(filePath: string): Promise<any>
读取文件。

**参数:**
- `filePath`: 文件路径

**返回:** 文件内容

##### deleteFile(filePath: string): Promise<boolean>
删除文件。

**参数:**
- `filePath`: 文件路径

**返回:** 删除是否成功

#### 导出功能

##### exportNovel(novelId: string, options: ExportOptions): Promise<string>
导出小说。

**参数:**
- `novelId`: 小说ID
- `options`: 导出选项

**返回:** 导出文件路径

**示例:**
```typescript
const filePath = await storageService.exportNovel(novelId, {
  format: 'markdown',
  includeMetadata: true,
  includeOutline: true,
  includeCharacters: true
});
```

##### createBackup(novelId: string): Promise<string>
创建备份。

**参数:**
- `novelId`: 小说ID

**返回:** 备份文件路径

##### restoreBackup(backupPath: string): Promise<void>
恢复备份。

**参数:**
- `backupPath`: 备份文件路径

##### getBackupHistory(novelId: string): Promise<BackupInfo[]>
获取备份历史。

##### deleteBackup(backupId: string): Promise<void>
删除备份。

#### 批量操作

##### exportMultipleNovels(novelIds: string[], options: ExportOptions): Promise<string[]>
批量导出小说。

##### createFullBackup(): Promise<string>
创建完整应用备份。

##### restoreFullBackup(backupPath: string): Promise<void>
恢复完整应用备份。

## IPC通信接口

### MainToRenderer（主进程到渲染进程）

主进程发送到渲染进程的消息类型。

```typescript
interface MainToRenderer {
  // 数据库更新
  'database-updated': (data: {
    table: string
    action: 'create' | 'update' | 'delete'
    id: string
  }) => void
  
  // 设置更新
  'settings-changed': (settings: AppSettings) => void
  
  // AI响应
  'ai-response': (response: AIResponse) => void
  
  // 文件操作结果
  'file-saved': (result: { success: boolean; path?: string; error?: string }) => void
  'file-exported': (result: { success: boolean; path?: string; error?: string }) => void
  
  // 进度更新
  'progress-update': (progress: {
    type: 'export' | 'backup' | 'ai-request'
    current: number
    total: number
    message: string
  }) => void
  
  // 错误通知
  'error-occurred': (error: {
    type: string
    message: string
    details?: any
  }) => void
}
```

### RendererToMain（渲染进程到主进程）

渲染进程发送到主进程的消息类型。

```typescript
interface RendererToMain {
  // 数据库操作
  'save-document': (document: Document) => Promise<void>
  'query-database': (query: { sql: string; params: any[] }) => Promise<any[]>
  
  // AI请求
  'ai-request': (request: AIRequest) => Promise<AIResponse>
  
  // 文件操作
  'save-file': (data: { content: any; path: string }) => Promise<boolean>
  'export-file': (data: { novelId: string; options: ExportOptions }) => Promise<string>
  
  // 设置操作
  'update-settings': (settings: Partial<AppSettings>) => Promise<void>
  'get-settings': () => Promise<AppSettings>
  
  // 应用控制
  'minimize-window': () => void
  'maximize-window': () => void
  'close-window': () => void
  
  // 系统操作
  'show-file-dialog': (options: any) => Promise<string[]>
  'show-message-box': (options: any) => Promise<number>
}
```

### IPC使用示例

#### 渲染进程发送消息
```typescript
// 在渲染进程中
const response = await window.electron.ipcRenderer.invoke(
  'ai-request',
  {
    prompt: '请续写以下内容：',
    context: currentText,
    maxTokens: 500
  }
);

// 监听主进程消息
window.electron.ipcRenderer.on('ai-response', (response) => {
  console.log('AI响应:', response);
});
```

#### 主进程处理消息
```typescript
// 在主进程中
ipcMain.handle('ai-request', async (event, request: AIRequest) => {
  try {
    const response = await aiService.generate(request);
    return response;
  } catch (error) {
    throw new Error(`AI请求失败: ${error.message}`);
  }
});

// 发送消息到渲染进程
mainWindow.webContents.send('ai-response', response);
```

## 状态管理接口

### NovelStore

小说状态管理。

```typescript
interface NovelStore {
  // 状态
  currentNovel: Novel | null
  novels: Novel[]
  currentChapter: Chapter | null
  chapters: Chapter[]
  
  // 操作
  setCurrentNovel: (novel: Novel | null) => void
  addNovel: (novel: Novel) => void
  updateNovel: (id: string, updates: Partial<Novel>) => void
  removeNovel: (id: string) => void
  loadNovels: () => Promise<void>
  
  setCurrentChapter: (chapter: Chapter | null) => void
  addChapter: (chapter: Chapter) => void
  updateChapter: (id: string, updates: Partial<Chapter>) => void
  removeChapter: (id: string) => void
  loadChapters: (novelId: string) => Promise<void>
}
```

### EditorStore

编辑器状态管理。

```typescript
interface EditorStore {
  // 状态
  content: string
  selection: Range | null
  wordCount: number
  isModified: boolean
  autoSaveEnabled: boolean
  
  // 操作
  setContent: (content: string) => void
  updateContent: (content: string) => void
  setSelection: (selection: Range | null) => void
  setModified: (modified: boolean) => void
  saveContent: () => Promise<void>
  loadContent: (content: string) => void
  
  // 历史记录
  undo: () => void
  redo: () => void
  canUndo: () => boolean
  canRedo: () => boolean
}
```

### SettingsStore

设置状态管理。

```typescript
interface SettingsStore {
  // 状态
  settings: AppSettings
  
  // 操作
  updateSettings: (updates: Partial<AppSettings>) => void
  resetSettings: () => void
  loadSettings: () => Promise<void>
  saveSettings: () => Promise<void>
  
  // AI提供商
  addAIProvider: (provider: AIProvider) => void
  removeAIProvider: (name: string) => void
  updateAIProvider: (name: string, updates: Partial<AIProvider>) => void
}
```

## 错误处理

### 错误类型

#### DatabaseError
数据库相关错误。

```typescript
class DatabaseError extends Error {
  constructor(message: string, public sql?: string, public params?: any[]) {
    super(message);
    this.name = 'DatabaseError';
  }
}
```

#### AIError
AI服务相关错误。

```typescript
class AIError extends Error {
  constructor(
    message: string,
    public provider?: string,
    public code?: string
  ) {
    super(message);
    this.name = 'AIError';
  }
}
```

#### FileError
文件操作相关错误。

```typescript
class FileError extends Error {
  constructor(
    message: string,
    public path?: string,
    public code?: string
  ) {
    super(message);
    this.name = 'FileError';
  }
}
```

### 错误处理策略

#### 数据库错误处理
```typescript
try {
  await db.createNovel(novelData);
} catch (error) {
  if (error instanceof DatabaseError) {
    console.error('数据库错误:', error.message);
    // 显示用户友好的错误消息
    showError('保存失败，请重试');
  } else {
    console.error('未知错误:', error);
    showError('发生未知错误');
  }
}
```

#### AI错误处理
```typescript
try {
  const response = await aiService.generate(request);
} catch (error) {
  if (error instanceof AIError) {
    if (error.code === 'RATE_LIMIT') {
      showError('AI服务请求过于频繁，请稍后再试');
    } else if (error.code === 'INVALID_API_KEY') {
      showError('API密钥无效，请检查设置');
    } else {
      showError('AI服务暂时不可用');
    }
  } else {
    showError('AI请求失败');
  }
}
```

#### 文件错误处理
```typescript
try {
  await storageService.exportNovel(novelId, options);
} catch (error) {
  if (error instanceof FileError) {
    if (error.code === 'NO_PERMISSION') {
      showError('没有文件写入权限，请选择其他位置');
    } else if (error.code === 'DISK_FULL') {
      showError('磁盘空间不足');
    } else {
      showError('文件导出失败');
    }
  } else {
    showError('导出过程中发生错误');
  }
}
```

## 使用示例

### 完整的使用流程示例

```typescript
// 1. 初始化服务
const db = new DatabaseService();
const aiService = new AIService();
const storageService = new StorageService(db);

// 2. 创建新小说
const novelId = await db.createNovel({
  title: '我的小说',
  author: '作者名',
  description: '这是一个示例小说',
  genre: ['奇幻'],
  tags: ['冒险'],
  status: 'draft',
  wordCount: 0,
  chapterCount: 0
});

// 3. 创建章节
const chapterId = await db.createChapter({
  novelId,
  title: '第一章',
  content: '这是第一章的内容...',
  chapterNumber: 1,
  wordCount: 100,
  status: 'draft',
  orderIndex: 0
});

// 4. 使用AI续写
const aiResponse = await aiService.continueWriting(
  '这是当前的故事内容...',
  300,
  '文学性'
);

// 5. 更新章节内容
await db.updateChapter(chapterId, {
  content: currentContent + aiResponse
});

// 6. 导出小说
const exportPath = await storageService.exportNovel(novelId, {
  format: 'markdown',
  includeMetadata: true,
  includeOutline: true
});

console.log('小说已导出到:', exportPath);
```

### 异步操作处理示例

```typescript
// 批量处理多个操作
async function processNovel(novelId: string) {
  try {
    // 并行加载相关数据
    const [novel, chapters, characters] = await Promise.all([
      db.getNovel(novelId),
      db.getChaptersByNovelId(novelId),
      db.getCharactersByNovelId(novelId)
    ]);

    if (!novel) {
      throw new Error('小说不存在');
    }

    // 处理每个章节
    for (const chapter of chapters) {
      try {
        // AI润色章节内容
        const polishedContent = await aiService.polishText(chapter.content);
        
        // 更新章节
        await db.updateChapter(chapter.id, {
          content: polishedContent,
          status: 'completed'
        });
      } catch (error) {
        console.error(`处理章节 ${chapter.title} 时出错:`, error);
        // 继续处理其他章节
      }
    }

    // 创建备份
    await storageService.createBackup(novelId);
    
    return { success: true, message: '处理完成' };
  } catch (error) {
    console.error('处理小说时出错:', error);
    return { success: false, message: error.message };
  }
}
```

---

这个API文档提供了小说创作管理器的完整接口定义和使用方法。开发者可以根据这些接口来扩展功能或集成到其他系统中。